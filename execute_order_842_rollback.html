<!DOCTYPE html>
<html>
<head>
    <title>執行訂單 842 回滾測試</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 3px; margin: 5px; }
        .button:hover { background: #005a87; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffffcc; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>執行訂單 842 回滾測試</h1>
        
        <div class="section info">
            <h3>📋 測試數據</h3>
            <table>
                <tr><th>項目</th><th>數值</th></tr>
                <tr><td>訂單ID</td><td>842</td></tr>
                <tr><td>用戶ID</td><td>1714</td></tr>
                <tr><td>用戶姓名</td><td>吳亮儀</td></tr>
                <tr><td>當前合夥等級ID</td><td>5 (高級合夥人)</td></tr>
                <tr><td>當前累積投資金額</td><td>13200.00</td></tr>
                <tr><td>訂單投資金額</td><td>8800.00</td></tr>
                <tr><td class="highlight">預期回滾後累積投資</td><td class="highlight">4400.00</td></tr>
                <tr><td class="highlight">預期回滾後等級ID</td><td class="highlight">4 (初級合夥人)</td></tr>
            </table>
        </div>

        <div class="section success">
            <h3>✅ 修復驗證</h3>
            <p>通過模擬測試確認：</p>
            <ul>
                <li>✅ 合夥等級計算邏輯正確</li>
                <li>✅ 投資金額計算正確</li>
                <li>✅ 等級降級邏輯正確</li>
                <li>✅ 4400 累積投資金額應該對應等級 4</li>
            </ul>
        </div>

        <div class="section">
            <h3>🔍 回滾前檢查</h3>
            <p>執行回滾前，請先檢查當前狀態：</p>
            <pre>SELECT id, name, partner_level_id, partner_accumulation,
       increasing_limit_consumption, point_increasable
FROM account 
WHERE id = 1714;</pre>
        </div>

        <div class="section warning">
            <h3>⚠️ 重要提醒</h3>
            <p>此操作將實際執行回滾，會影響資料庫數據：</p>
            <ul>
                <li>partner_accumulation 將從 13200 減少到 4400</li>
                <li>partner_level_id 將從 5 降級到 4</li>
                <li>相關的積分和圓滿點數記錄將被回滾</li>
                <li>請確保在測試環境中執行</li>
            </ul>
        </div>

        <div class="section">
            <h3>🚀 執行回滾</h3>
            <p>點擊下方按鈕執行訂單 842 的回滾操作：</p>
            <a href="/order_ctrl/rollback_pointback?id=842" class="button danger" onclick="return confirm('確定要執行訂單 842 的回滾操作嗎？\n\n這將會：\n- 將用戶 1714 的 partner_accumulation 從 13200 減少到 4400\n- 將 partner_level_id 從 5 降級到 4\n- 回滾所有相關的積分記錄\n\n請確認要繼續執行。')">
                執行訂單 842 回滾
            </a>
        </div>

        <div class="section">
            <h3>📊 回滾後檢查</h3>
            <p>執行回滾後，請檢查結果：</p>
            
            <h4>1. 檢查用戶狀態變化</h4>
            <pre>SELECT id, name, partner_level_id, partner_accumulation,
       increasing_limit_consumption, point_increasable
FROM account 
WHERE id = 1714;</pre>

            <h4>2. 檢查訂單狀態</h4>
            <pre>SELECT id, user_id, do_award_time 
FROM orderform 
WHERE id = 842;</pre>

            <h4>3. 檢查合夥等級關係記錄</h4>
            <pre>SELECT * FROM partner_level_relation 
WHERE user_id = 1714 
ORDER BY datetime DESC 
LIMIT 5;</pre>

            <h4>4. 驗證等級對應關係</h4>
            <pre>SELECT pl.id, pl.name, pl.contribution,
       CASE WHEN 4400 >= pl.contribution THEN '✓' ELSE '✗' END as 符合條件
FROM partner_level pl
ORDER BY pl.contribution;</pre>
        </div>

        <div class="section success">
            <h3>✅ 預期結果</h3>
            <p>如果修復成功，應該看到：</p>
            <ul>
                <li>✅ account.partner_accumulation = 4400.********</li>
                <li>✅ account.partner_level_id = 4</li>
                <li>✅ orderform.do_award_time = 空值（已清除）</li>
                <li>✅ partner_level_relation 表有新的等級 4 記錄</li>
                <li>✅ 相關的積分記錄已被刪除</li>
            </ul>
        </div>

        <div class="section">
            <h3>🔄 恢復操作</h3>
            <p>如果需要恢復到回滾前的狀態：</p>
            <a href="/order_ctrl/do_pointback?id=842" class="button" onclick="return confirm('確定要重新執行訂單 842 的積分回饋嗎？')">
                重新執行訂單 842 積分回饋
            </a>
        </div>
    </div>
</body>
</html>
