<?php

namespace App\Services\Order;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 積分回饋回滾服務
 * 負責處理訂單積分回饋的回滾操作，恢復所有相關的資料庫狀態
 * ---
 * 這裡的code跟別的地方不一樣..至少符合PSR-12了
 * 希望這是屎山中的一點點光明，未來等model跟repository建好
 * DB部分才能搬移..到時，我應該也不在了XDD
 */
class RollbackPointbackService
{
    /**
     * 執行積分回饋回滾
     *
     * @param array $orderformIds 要回滾的訂單ID陣列
     * @return array 回滾結果
     * @throws Exception
     */
    public function rollbackPointback(array $orderformIds): array
    {
        $results = [];

        // 檢查要取消回饋的訂單們
        $orderforms = $this->validateOrderforms($orderformIds);

        foreach ($orderforms as $orderform) {
            try {
                DB::connection('main_db')->beginTransaction();

                $result = $this->rollbackSingleOrder($orderform);
                $results[] = $result;

                DB::connection('main_db')->commit();

                // Log::info("積分回饋回滾成功", [
                //     'order_id' => $orderform->id,
                //     'user_id' => $orderform->user_id,
                //     'award_time' => $orderform->do_award_time
                // ]);
            } catch (Exception $e) {
                DB::connection('main_db')->rollback();

                Log::error("積分回饋回滾失敗", [
                    'order_id' => $orderform->id,
                    'error' => $e->getMessage()
                ]);

                throw new Exception("訂單 {$orderform->id} 回滾失敗: " . $e->getMessage());
            }
        }

        return $results;
    }

    /**
     * 驗證訂單狀態
     *
     * @param array $orderformIds
     * @return array
     * @throws Exception
     */
    private function validateOrderforms(array $orderformIds): array
    {
        $orderforms = [];

        foreach ($orderformIds as $orderformId) {
            $orderform = DB::connection('main_db')
                ->table('orderform')
                ->where('id', $orderformId)
                ->first();

            // 一張訂單出錯就整個throw出去真的好嗎？
            // 但在大量匯入時，如果不中斷，最後才return哪些訂單出錯..會不會為了找出錯誤訂單傷眼睛?
            if (!$orderform) {
                throw new Exception("訂單 {$orderformId} 不存在");
            }

            if (!$orderform->do_award_time) {
                throw new Exception("訂單 {$orderformId} 尚未處理積分回饋，無需回滾");
            }

            $orderforms[] = $orderform;
        }

        return $orderforms;
    }

    /**
     * 回滾單一訂單
     *
     * @param object $orderform
     * @return array
     */
    private function rollbackSingleOrder(object $orderform): array
    {
        $orderId = $orderform->id;
        $awardTime = $orderform->do_award_time;

        // 1. 取得要回滾的記錄
        $records = $this->getPointbackRecords($orderId, $awardTime);
        // 2. 執行回滾
        $rollbackResult = $this->executeRollback($records);

        // 3. 清除訂單回饋時間
        DB::connection('main_db')
            ->table('orderform')
            ->where('id', $orderId)
            ->update(['do_award_time' => '']);

        // 4. 恢復增值積分價值
        $this->restorePointIncreasableValue($records['point_increasable_records']);

        return [
            'order_id' => $orderId,
            'award_time' => $awardTime,
            'rollback_result' => $rollbackResult
        ];
    }

    /**
     * 取得積分回饋相關記錄
     *
     * @param int $orderId
     * @param string $awardTime
     * @return array
     */
    private function getPointbackRecords(int $orderId, string $awardTime): array
    {
        // 現金積分記錄
        $pointsRecords = DB::connection('main_db')
            ->table('points_record')
            ->where('belongs_time', $awardTime)
            ->where(function ($query) {
                $query->where('msg', 'like', "%線上消費回饋%")
                    ->orWhere('msg', 'like', "%達可增值上限拋轉現金積分%")
                    ->orWhere('msg', 'like', "%個人責任額檢查未達標(拋轉現金積分)%");
            })
            ->get()
            ->toArray();

        // 增值積分記錄
        $pointIncreasableRecords = DB::connection('main_db')
            ->table('point_increasable_record')
            ->where('create_time', $awardTime)
            ->where(function ($query) {
                $query->where('msg', 'like', "%線上消費回饋%")
                    ->orWhere('msg', 'like', "%達可增值上限拋轉現金積分%")
                    ->orWhere('msg', 'like', "%個人責任額檢查未達標(拋轉現金積分)%")
                    ->orWhere('msg', 'like', "%自動升級合夥人等級%");
            })
            ->get()
            ->toArray();

        // 圓滿點數記錄
        $increasingLimitRecords = DB::connection('main_db')
            ->table('increasing_limit_record')
            ->where('create_time', $awardTime)
            ->where(function ($query) {
                $query->where('type', 1) // 處理回饋類型
                    ->orWhere('type', 2) // 處理增值轉換類型
                    ->orWhere('type', 3); // 自動升級合夥人等級類型
            })
            ->where(function ($query) {
                $query->where('msg', 'like', "%線上消費回饋消費圓滿點數%")
                    ->orWhere('msg', 'like', "%線上消費回饋其他圓滿點數%")
                    ->orWhere('msg', 'like', "%線上消費回饋功德圓滿點數%")
                    ->orWhere('msg', 'like', "%達可增值上限拋轉現金積分%")
                    ->orWhere('msg', 'like', "%個人責任額檢查未達標(拋轉現金積分)%")
                    ->orWhere('msg', 'like', "%自動升級合夥人等級%")
                    ->orWhere('msg', 'like', "%積分增值折抵消費圓滿點數%")
                    ->orWhere('msg', 'like', "%積分增值折抵其他圓滿點數%")
                    ->orWhere('msg', 'like', "%積分增值折抵功德圓滿點數%");
            })
            ->get()
            ->toArray();

        // 資金池記錄
        $poolRecords = DB::connection('main_db')
            ->table('point_increasable_pool')
            ->where('orderform_id', $orderId)
            ->where('datetime', $awardTime)
            ->get()
            ->toArray();

        // 月分紅認列記錄
        $dividendMonthRecords = DB::connection('main_db')
            ->table('dividend_month_record')
            ->where('orderform_id', $orderId)
            ->get()
            ->toArray();

        // VIP等級變更記錄
        $awardDatetime = date('Y-m-d H:i:s', $awardTime);
        $vipRelations = DB::connection('main_db')
            ->table('vip_type_relation')
            ->where('datetime', '>=', $awardDatetime)
            ->where('datetime', '<=', date('Y-m-d H:i:s', $awardTime + 60))
            ->get()
            ->toArray();

        // 合夥人等級變更記錄
        $partnerRelations = DB::connection('main_db')
            ->table('partner_level_relation')
            ->where('datetime', '>=', $awardDatetime)
            ->where('datetime', '<=', date('Y-m-d H:i:s', $awardTime + 60))
            ->get()
            ->toArray();

        // 訂單商品記錄（用於計算需要回滾的投資金額）
        $orderProducts = DB::connection('main_db')
            ->table('orderform_product')
            ->where('orderform_id', $orderId)
            ->get()
            ->toArray();

        return [
            'points_records' => $pointsRecords,
            'point_increasable_records' => $pointIncreasableRecords,
            'increasing_limit_records' => $increasingLimitRecords,
            'pool_records' => $poolRecords,
            'dividend_month_records' => $dividendMonthRecords,
            'vip_relations' => $vipRelations,
            'partner_relations' => $partnerRelations,
            'order_products' => $orderProducts
        ];
    }

    /**
     * 執行回滾操作
     *
     * @param array $records
     * @return array
     */
    private function executeRollback(array $records): array
    {
        $result = [
            'deleted_records' => 0,
            'updated_accounts' => 0
        ];

        // 1.回滾現金積分
        foreach ($records['points_records'] as $record) {
            $this->rollbackPointsRecord($record);
            $result['deleted_records']++;
        }

        // 2.回滾增值積分
        foreach ($records['point_increasable_records'] as $record) {
            $this->rollbackPointIncreasableRecord($record);
            $result['deleted_records']++;
        }

        // 3.回滾圓滿點數
        foreach ($records['increasing_limit_records'] as $record) {
            $this->rollbackIncreasingLimitRecord($record);
            $result['deleted_records']++;
        }

        // 4.回滾資金池記錄
        foreach ($records['pool_records'] as $record) {
            $this->rollbackPoolRecord($record);
            $result['deleted_records']++;
        }

        // 5.回滾月分紅認列記錄
        foreach ($records['dividend_month_records'] as $record) {
            $this->rollbackDividendMonthRecord($record);
            $result['deleted_records']++;
        }

        // 6.回滾VIP關係記錄
        foreach ($records['vip_relations'] as $record) {
            $this->rollbackVipRelation($record);
            $result['deleted_records']++;
        }

        // 7.回滾合夥人關係記錄
        foreach ($records['partner_relations'] as $record) {
            $this->rollbackPartnerRelation($record);
            $result['deleted_records']++;
        }

        // 8.回滾合夥人累積投資金額
        $this->rollbackPartnerAccumulation($records['order_products']);

        // 9.檢查並降級合夥人等級
        $this->checkAndDowngradePartnerLevel($records['order_products']);

        return $result;
    }

    /**
     * 回滾現金積分記錄
     */
    private function rollbackPointsRecord(object $record): void
    {
        // 恢復會員現金積分
        DB::connection('main_db')
            ->table('account')
            ->where('id', $record->user_id)
            ->decrement('point', $record->points);

        // 刪除記錄
        DB::connection('main_db')
            ->table('points_record')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾增值積分記錄
     */
    private function rollbackPointIncreasableRecord(object $record): void
    {
        // 恢復會員增值積分
        DB::connection('main_db')
            ->table('account')
            ->where('id', $record->user_id)
            ->decrement('point_increasable', $record->num);

        // 刪除記錄
        DB::connection('main_db')
            ->table('point_increasable_record')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾圓滿點數記錄
     */
    private function rollbackIncreasingLimitRecord(object $record): void
    {
        $field = match ($record->limit_type) {
            1 => 'increasing_limit_invest',
            2 => 'increasing_limit_consumption',
            3 => 'increasing_limit_other',
            default => throw new Exception("未知的 limit_type: {$record->limit_type}")
        };

        // 恢復會員圓滿點數（注意：記錄中的 num 可能是負數）
        if ($record->num > 0) {
            DB::connection('main_db')
                ->table('account')
                ->where('id', $record->user_id)
                ->decrement($field, $record->num);
        } else {
            DB::connection('main_db')
                ->table('account')
                ->where('id', $record->user_id)
                ->increment($field, abs($record->num));
        }

        // 刪除記錄
        DB::connection('main_db')
            ->table('increasing_limit_record')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾資金池記錄
     */
    private function rollbackPoolRecord(object $record): void
    {
        // 恢復資金池 - 注意：這裡不需要恢復資金池總額，因為記錄本身就是資金池的一部分
        // 直接刪除記錄即可，資金池總額會通過 sum() 重新計算

        // 刪除記錄
        DB::connection('main_db')
            ->table('point_increasable_pool')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾月分紅認列記錄
     */
    private function rollbackDividendMonthRecord(object $record): void
    {
        // 刪除記錄
        DB::connection('main_db')
            ->table('dividend_month_record')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾VIP等級變更記錄
     */
    private function rollbackVipRelation(object $record): void
    {
        // 刪除記錄
        DB::connection('main_db')
            ->table('vip_type_relation')
            ->where('id', $record->id)
            ->delete();

        // 恢復VIP等級到變更前的狀態
        $previousVipType = DB::connection('main_db')
            ->table('vip_type_relation')
            ->where('user_id', $record->user_id)
            ->where('id', '<', $record->id)
            ->orderBy('id', 'desc')
            ->first();

        $restoreVipType = $previousVipType ? $previousVipType->vip_type_id : 0;
        DB::connection('main_db')
            ->table('account')
            ->where('id', $record->user_id)
            ->update(['vip_type' => $restoreVipType]);
    }

    /**
     * 回滾合夥人等級變更記錄
     */
    private function rollbackPartnerRelation(object $record): void
    {
        // 合夥人等級關係記錄只需要刪除，不需要恢復舊值
        // 因為這個表只是記錄等級變更的歷史，實際的等級資訊在 account 表中
        // 等級的恢復會通過重新計算合夥人累積投資金額來處理

        // 刪除記錄
        DB::connection('main_db')
            ->table('partner_level_relation')
            ->where('id', $record->id)
            ->delete();
    }

    /**
     * 回滾合夥人累積投資金額
     */
    private function rollbackPartnerAccumulation(array $orderProducts): void
    {
        // 先收集所有訂單ID，然後查詢對應的用戶ID
        $orderformIds = array_unique(array_map(function ($product) {
            return $product->orderform_id;
        }, $orderProducts));

        // 查詢訂單對應的用戶ID
        $orderformUsers = DB::connection('main_db')
            ->table('orderform')
            ->whereIn('id', $orderformIds)
            ->pluck('user_id', 'id')
            ->toArray();

        // 計算需要回滾的投資金額
        $userInvestments = [];

        foreach ($orderProducts as $product) {
            if ($product->product_cate == 1) { // 投資商品
                // 計算校正CV金額（與 BonusHelper->get_count_cv 相同的邏輯）
                $countCv = $product->price_cv - $product->deduct_invest - $product->deduct_consumption;

                if ($countCv > 0) {
                    $userId = $orderformUsers[$product->orderform_id] ?? 0;
                    if ($userId > 0) {
                        if (!isset($userInvestments[$userId])) {
                            $userInvestments[$userId] = 0;
                        }
                        $userInvestments[$userId] += $countCv;
                    }
                }
            }
        }

        // 從各用戶的 partner_accumulation 中減去相應的投資金額
        foreach ($userInvestments as $userId => $investmentAmount) {
            DB::connection('main_db')
                ->table('account')
                ->where('id', $userId)
                ->decrement('partner_accumulation', $investmentAmount);
        }
    }

    /**
     * 檢查並降級合夥人等級
     */
    private function checkAndDowngradePartnerLevel(array $orderProducts): void
    {
        // 先收集所有訂單ID，然後查詢對應的用戶ID
        $orderformIds = array_unique(array_map(function ($product) {
            return $product->orderform_id;
        }, $orderProducts));

        // 查詢訂單對應的用戶ID
        $orderformUsers = DB::connection('main_db')
            ->table('orderform')
            ->whereIn('id', $orderformIds)
            ->pluck('user_id', 'id')
            ->toArray();

        // 計算需要回滾的投資金額
        $userInvestments = [];

        foreach ($orderProducts as $product) {
            if ($product->product_cate == 1) { // 投資商品
                // 計算校正CV金額（與 BonusHelper->get_count_cv 相同的邏輯）
                $countCv = $product->price_cv - $product->deduct_invest - $product->deduct_consumption;

                if ($countCv > 0) {
                    $userId = $orderformUsers[$product->orderform_id] ?? 0;
                    if ($userId > 0) {
                        if (!isset($userInvestments[$userId])) {
                            $userInvestments[$userId] = 0;
                        }
                        $userInvestments[$userId] += $countCv;
                    }
                }
            }
        }

        // 載入合夥等級資料
        $partnerLevels = DB::connection('main_db')
            ->table('partner_level')
            ->orderBy('contribution', 'asc')
            ->get()
            ->keyBy('id')
            ->toArray();

        // 檢查每個用戶是否需要降級
        foreach ($userInvestments as $userId => $investmentAmount) {
            $user = DB::connection('main_db')
                ->table('account')
                ->where('id', $userId)
                ->first(['id', 'partner_level_id', 'partner_accumulation']);

            if (!$user || $user->partner_level_id == 0) {
                continue; // 用戶不存在或沒有合夥等級
            }

            // 計算回滾後的累積投資金額
            $newAccumulation = $user->partner_accumulation - $investmentAmount;

            // 根據新的累積投資金額計算應該的等級
            $newLevelId = $this->calculatePartnerLevel($newAccumulation, $partnerLevels);

            // 如果等級需要降級
            if ($newLevelId < $user->partner_level_id) {
                // 更新用戶的合夥等級
                DB::connection('main_db')
                    ->table('account')
                    ->where('id', $userId)
                    ->update(['partner_level_id' => $newLevelId]);

                // 記錄等級變更到 partner_level_relation 表
                if ($newLevelId > 0) {
                    $this->createPartnerLevelRelation($userId, $newLevelId);
                }

                // Log::info("用戶 {$userId} 合夥等級從 {$user->partner_level_id} 降級到 {$newLevelId}，累積投資金額: {$newAccumulation}");
            }
        }
    }

    /**
     * 根據累積投資金額計算對應的合夥等級ID
     */
    private function calculatePartnerLevel(float $totalContribution, array $partnerLevels): int
    {
        $levelId = 0; // 預設無等級
        foreach ($partnerLevels as $level) {
            if ($totalContribution >= $level->contribution) {
                $levelId = $level->id;
            } else {
                break; // 因為已按 contribution 排序，可以提前結束
            }
        }

        return $levelId;
    }

    /**
     * 創建合夥等級關係記錄
     */
    private function createPartnerLevelRelation(int $userId, int $levelId): void
    {
        // 檢查是否已存在相同記錄（避免重複）
        $exists = DB::connection('main_db')
            ->table('partner_level_relation')
            ->where('user_id', $userId)
            ->where('level_id', $levelId)
            ->exists();

        if (!$exists) {
            DB::connection('main_db')
                ->table('partner_level_relation')
                ->insert([
                    'user_id' => $userId,
                    'level_id' => $levelId,
                    'datetime' => date('Y-m-d H:i:s')
                ]);
        }
    }

    /**
     * 恢復增值積分價值
     */
    private function restorePointIncreasableValue(array $pointIncreasableRecords): void
    {
        if (empty($pointIncreasableRecords)) {
            return;
        }

        // 重新計算增值積分總量
        $totalPointIncreasable = DB::connection('main_db')
            ->table('account')
            ->sum('point_increasable');

        // 重新計算資金池總量
        $totalPool = DB::connection('main_db')
            ->table('point_increasable_pool')
            ->sum('num');

        // 更新增值積分現值
        if ($totalPointIncreasable > 0) {
            $newValue = $totalPool / $totalPointIncreasable;
            DB::connection('main_db')
                ->table('bonus_setting')
                ->where('id', 1)
                ->update(['value' => $newValue]);
        }
    }
}
