<?php

namespace App\Services\Order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DeleteOrderService
{
    public function __construct(private RollbackPointbackService $rollbackService) {}

    /**
     * 處理 Excel 檔案並刪除訂單
     *
     * @param Request $request HTTP 請求
     * @return array 包含成功狀態和錯誤訊息的陣列
     */
    public function deleteByImport($importOrderData): array
    {
        try {
            // 刪除訂單
            $errorData = $this->deleteOrdersByImport($importOrderData);

            if (count($errorData) > 0) {
                $errorMessages = [];
                foreach ($errorData as $item) {
                    $errorMessages[] = "<small>訂單(" . $item['data']['create_time'] . "/" . $item['data']['user_id'] . ")：" . $item['error_msg'] . "</small>";
                }
                return [
                    'success' => false,
                    'message' => '刪除失敗',
                    'errors' => implode("<br>", $errorMessages)
                ];
            }

            return ['success' => true, 'message' => '刪除成功'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => '處理失敗：' . $e->getMessage()];
        }
    }

    /**
     * 解析 Excel 檔案
     *
     * @param string $filename 檔案路徑
     * @return array 訂單資料陣列
     */
    public function parseExcelFile(string $filename): array
    {
        $phpReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx");
        $phpExcel = $phpReader->load($filename);
        $sheet = $phpExcel->getSheet(0);
        $allRow = $sheet->getHighestRow(); // 取得最大的行號

        $orderData = [];
        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
            $data = [];

            $createTime = trim($phpExcel->getActiveSheet()->getCell("A" . $currentRow)->getCalculatedValue()); // 訂單日期
            $data['create_time'] = $createTime;
            $data['user_id'] = trim($phpExcel->getActiveSheet()->getCell("B" . $currentRow)->getCalculatedValue()); // 消費者(會員編號)
            $data['transport_location_name'] = trim($phpExcel->getActiveSheet()->getCell("C" . $currentRow)->getCalculatedValue()); // 消費者姓名
            $data['transport_email'] = trim($phpExcel->getActiveSheet()->getCell("D" . $currentRow)->getCalculatedValue()); // 消費者email
            $data['transport_location_phone'] = trim($phpExcel->getActiveSheet()->getCell("E" . $currentRow)->getCalculatedValue()); // 消費者手機
            $data['transport_location'] = trim($phpExcel->getActiveSheet()->getCell("F" . $currentRow)->getCalculatedValue()); // 消費者地址
            $data['total'] = trim($phpExcel->getActiveSheet()->getCell("K" . $currentRow)->getCalculatedValue()); // 訂單總金額

            if (!$data['create_time'] && !$data['user_id']) {
                break;
            }
            $orderData[] = $data;
        }

        return $orderData;
    }

    /**
     * 依匯入資料刪除訂單
     *
     * @param array $importOrderData 訂單資料陣列
     * @return array 錯誤資料陣列
     */
    public function deleteOrdersByImport(array $importOrderData): array
    {
        $userCollection = DB::connection('main_db')->table('account')->get();
        $dbNumberToUser = $userCollection->keyBy('number')->toArray(); // 將會員編號轉為id的對應關係

        $errorData = [];

        foreach ($importOrderData as $data) {
            $originalData = json_decode(json_encode($data), true);
            try {
                // 1. 驗證資料
                if (($data['create_time'] ?? '') == '') {
                    $errorData[] = ['data' => $originalData, 'error_msg' => '未設定訂單時間'];
                    continue;
                }

                // 2. 轉換會員編號為 ID
                $userId = null;

                if ($data['user_id'] ?? '') {
                    if (!isset($dbNumberToUser[$data['user_id']])) {
                        $errorData[] = ['data' => $originalData, 'error_msg' => '無此消費者'];
                        continue;
                    } else {
                        $userId = $dbNumberToUser[$data['user_id']]->id;
                    }
                }

                // 3. 建立查詢條件並刪除訂單
                $this->buildQueryAndDelete($data, $userId);
            } catch (\Exception $e) {
                $errorData[] = ['data' => $originalData, 'error_msg' => '處理錯誤：' . $e->getMessage()];
            }
        }

        return $errorData;
    }

    /**
     * 建立查詢條件並執行刪除
     *
     * @param array $data 訂單資料
     * @param int|null $userId 使用者ID
     * @return void
     */
    private function buildQueryAndDelete(array $data, ?int $userId): void
    {
        $query = DB::connection('main_db')->table('orderform');

        // 依訂單日期查詢 (精確到日期)
        if ($data['create_time']) {
            $query->where('create_time', strtotime($data['create_time']));
        }

        // 依會員 ID 查詢
        if ($userId !== null) {
            $query->where('user_id', $userId);
        }

        // 依收件人姓名查詢
        if ($data['transport_location_name'] ?? '') {
            $query->where('transport_location_name', $data['transport_location_name']);
        }

        // 依收件人手機查詢
        if ($data['transport_location_phone'] ?? '') {
            $query->where('transport_location_phone', $data['transport_location_phone']);
        }

        // 依收件人地址查詢
        if ($data['transport_location'] ?? '') {
            $query->where('transport_location', $data['transport_location']);
        }

        // 依收件人Email查詢
        if ($data['transport_email'] ?? '') {
            $query->where('transport_email', $data['transport_email']);
        }

        // 依訂單金額查詢
        if ($data['total'] ?? '') {
            $query->where('total', $data['total']);
        }

        try {
            $res = $query->get();
            foreach ($res as $order) {
                if ($order->do_award_time != '') {
                    $this->rollbackService->rollbackPointback([$order->id]);
                }
            }
            $query->delete();
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
