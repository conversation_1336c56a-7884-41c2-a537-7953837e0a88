<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Admin extends MainController{
  const PER_PAGE_ROWS = 5;
  const SIMPLE_MODE_PAGINATE = false;
  private $DBTextConnecter;
  private $resTableName;
  protected $DBadmin_info;
  protected $DBFileConnecter;
  public function __construct(){
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('admin');
    $this->DBadmin_info = DBTextConnecter::withTableName('admin_info');
    $this->DBFileConnecter = DBFileConnecter::withTableName('admin_info');
    $this->resTableName = 'admin';
  }

  public function edit(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }

    $accounts = Db::table($this->resTableName)
      ->whereRaw("permission != 'all' ")
      ->paginate(
        self::PER_PAGE_ROWS,
      );

    if (empty($accounts->items()) == false) {
      foreach ($accounts->items() as $key => $item) {
        if (empty($item->purview)) {
          $item->purview = '[]';
        }
      }
    }

    $this->data['accounts'] = $accounts;
    return view('admin.admin.account', ['data' => $this->data]);
  }

  public function point_set(Request $request){
    /*使用期限*/
    $this->data['limit_time'] = config('control.control_point_duration');

    /*兌換比率*/
    $point_rate = Db::table('points_setting')->find(3)->value;
    $this->data['point_rate'] = $point_rate;

    /*適用分館*/
    $use_product = Db::table('points_allow_use')->whereRaw($this->distributor_id_where_sql)->first();
    if($use_product){
      $use_product = $use_product->value ? $use_product->value : "[]";
    }else{
      $use_product = "[]";
    }
    $this->data['use_product'] = $use_product;

    return view('admin.admin.point_set',['data'=>$this->data]);
  }
  public function point_set_update(Request $request){
    if($this->admin_type=='admin'){
      /*儲存使用期限*/
      $limit_time = $request->post('limit_time') ?? null;
      try{
        if($limit_time!==null){
          Db::connection('main_db')->table('excel')->whereRaw("id='2'")->update(['value1'=>$limit_time]);
        }
      } catch (\Exception $e){
        $this->dumpException($e);
      }

      /*儲存兌換比率*/
      $point_rate = $request->post('point_rate') ?? null;
      try{
        if($point_rate!==null){
          Db::table('points_setting')->whereRaw("id='3'")->update(['value'=>$point_rate]);
        }	
      } catch (\Exception $e){
        $this->dumpException($e);
      }
    }

    // 儲存適用分館
    $use_product = empty($request->post('use_product'))===true ? [] : $request->post('use_product');
    if($this->admin_type=='distribution'){
      $my_points_allow_use = $this->distributor_id_where_sql;
      $my_points_allow = Db::table('points_allow_use')->where($my_points_allow_use)->first();
      if(!$my_points_allow){
        Db::table('points_allow_use')->where($my_points_allow_use)->insert([
          'distributor_id' => session()->get($this->admin_type)['id'],
          'value' => json_encode($use_product),
        ]);
        $this->success(Lang::get('操作成功'));
      }

    }else{
      $my_points_allow_use = 'distributor_id=0';
    }
    Db::table('points_allow_use')->whereRaw($my_points_allow_use)->update(['value'=> json_encode($use_product)]);

    $this->success(Lang::get('操作成功'));
  }
    
  public function maxlifetime_set(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $this->data['auto_logout_time'] = config('control.control_auto_logout');
    return view('admin.admin.maxlifetime_set',['data'=>$this->data]);
  }
  public function maxlifetime_update(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $value1 = $request->post('value1');
    try{
      Db::connection('main_db')->table('excel')->where("id",5)->update(['value1'=>$value1]);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->redirect(url('/admin/admin/maxlifetime_set'));
  }
    
  public function add(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $insert = $request->post();
    unset($insert['_token']);
    $insert['purview'] = json_encode($inser['purview']??'');

    $rule = [
      'name' 	   => 'required',
      'account'  => 'required',
      'password' => 'required',
      // 'email'	   => 'required',
    ];
    $msg = [
      'name.required' => Lang::get('請輸入姓名'),
      'account.required' => Lang::get('帳號不得為空'),
      'password.required' => Lang::get('密碼不得為空'),
      // 'email.required' => Lang::get('請輸入email'),
    ];

    $validate = Validator::make($insert,$rule,$msg);

    // 避免$insert之key遺失問題
    $data = [];

    foreach ($rule as $key => $value) {
      $data[$key] = $value;
    }

    if ($validate->fails()) {
      $this->error($validate->errors()->first());
    }
      
    try{
      Db::table($this->resTableName)
      ->insert([
        'name' =>  $insert['name'],
        'account' =>  $insert['account'],
        'password' =>  md5($insert['password']),
        'originalPassword' =>  $insert['password'],
        'email' =>  $insert['email'],
        'permission' =>  'no',
        'purview' =>  $insert['purview']
      ]);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success(Lang::get('操作成功'));
  }
  public function del(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $id = $request->get('id');
    $acc = Db::table($this->resTableName)->find($id);
    $acc = CommonService::objectToArray($acc);
    if($acc){
      if($id==1){ $this->error(Lang::get('您無法操作此項目')); }
      if($acc['permission']=='current'){ $this->error(Lang::get('您無法操作此項目')); }
    }
    // dd($acc);
    
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success(Lang::get('操作成功'));
  }
  public function update(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $new_password = $request->post('new_password');
    $rep_password = $request->post('rep_password');
    $old_password = $request->post('old_password');
    $id = $request->get('id');
    $adminData = Db::table('admin')->find($id);
    $adminData = CommonService::objectToArray($adminData);
    if($adminData['password'] != md5($old_password)){
      $this->dumpError(Lang::get('資料有誤'));
    }
    $rule = [
      'new_password' => 'required|same:rep_password'
    ];
    $msg = [
      'new_password.required' => Lang::get('密碼不得為空'),
      'new_password.same'=> Lang::get('密碼不一致'),
    ];
    $data = [
      'rep_password' => $rep_password,
      'new_password' => $new_password,
    ];

    $validate = Validator::make($data,$rule,$msg);

    if ($validate->fails()) {
      $this->dumpError($validate->errors()->first());
    }
    $updateData = [
      // 'id' => 1,
      'originalPassword' => $new_password,
      'password' => md5($new_password)
    ];
    // dd($updateData);
    try{
      Db::table($this->resTableName)->whereRaw("id = '".$id."'")->update($updateData);
      // $this->DBTextConnecter->setDataArray($updateData);
      // $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success(Lang::get('操作成功'));
  }
  public function current_change(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $id = $request->post('id');
    
    try{
      Db::table($this->resTableName)->whereRaw("permission = 'current'")->update(['permission'=>'no']);
      Db::table($this->resTableName)->whereRaw("id = '".$id."'")->update(['permission'=>'current']);
      return [
        'status' => true,
        'message' => Lang::get('操作成功'),
      ];

    }catch (\Exception $e){
      return [
        'status' => false,
        'message' => $e->getMessage(),
      ];
    }
  }
  public function update_purview(Request $request) {
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $id = $request->post('up_id');
    if($id == 2 && session()->get('admin')['id'] != 1){
      $this->error(Lang::get('不可修改管理員權限'));
    }

    $purview = $request->post('update_purview')??'';

    // $default_purview=DB::table('admin')->select('purview')->where('id',2)->first();

    // $old_purview = json_decode($default_purview->purview,true);
    // foreach($old_purview as $key1 => $o_purview){


    // 	foreach($purview as $key2 => $pu){
    // 		if($key1==$key2){
    // 			foreach($pu as $v){
    // 				if(!in_array($v,$o_purview)){
    // 					array_push($old_purview[$key1],$v);
    // 				}
    // 			}		
    // 		}else if(!in_array($key2,array_keys($old_purview))){

    // 			//array_push($old_purview[$key2],$pu);
    // 			$old_purview[$key2] = $pu;
    // 		}
    // 	}			
      
    // }
    /* dump($old_purview);
    dump($purview);
    dd('end'); */
    // $purview = json_encode($old_purview);
    $purview = json_encode($purview);

    $updateData = [
      'id' => $id,
      'purview' =>$purview
    ];
    try{
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success(Lang::get('操作成功'), '/admin/admin/edit');
  }
  public function emailUpdate(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $id = $request->post('id');
    $email = $request->post('email');
    try{
      $this->DBTextConnecter->setDataArray([
        'id' => $id,
        'email' => $email
      ]);
      $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e){
      return ['status' => false,'message' => $e->getMessage(),];
    }
    return ['status' => true,'message' => Lang::get('操作成功')];
  }

  public function admin_info(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $admin_info = Db::table('admin_info')->find(1);
    $this->data['admin_info'] = CommonService::objectToArray($admin_info);
    return view('admin.admin.admin_info',['data'=>$this->data]);
  }
  public function admin_info_update(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $newData = $request->post();
    unset($newData['_token']);
    $picNameList = ['customer_logo', 'system_logo', 'marketing_logo', 'favicon', 'success_logo', 'error_logo'];
    $file = $request->file();
    // dd($file);

    try{
      for($i = 1; $i <= count($picNameList); $i++) {
        if( $newData['del_' . $picNameList[ $i-1 ]] ){
          $newData[$picNameList[ $i-1 ]] = '';
          unset($file[$picNameList[ $i-1 ]]);
        }
        unset($newData['del_' .$picNameList[ $i-1 ]]);
      }
    } catch (\Exception $e) {
      $this->dumpException($e);
    }
    // dump($file);exit;
    try{
      $this->DBadmin_info->setDataArray($newData);
      $this->DBadmin_info->upTextRow();
      if(count($file)>0 && !empty($file)){
        $this->DBFileConnecter->setFileArray($file);
        $this->DBFileConnecter->setPrivateKeyId($newData['id']);
        $this->DBFileConnecter->upFileRow();
      }


        // foreach($file as $key => $file){
        // 	$filename=$file->getClientOriginalName();					
        // 	$ext = pathinfo($filename, PATHINFO_EXTENSION);  
          
        //     $new_file = "/public/upload/".$key."_" . $newData['id'] . "_" . time() . '.'.$ext;
        // 	$new_file_save = base_path().$new_file;
        // 	$file_tmp = $file->getPathName();
          
        //     $doupload=move_uploaded_file($file_tmp, $new_file_save);
        //     $newData[$key] = $new_file;					
          
        // }
        
        // DB::table('admin_info')->find($newData['id'])->update($newData);
        
      // }else{}


      
    } catch (\Exception $e) {
      $this->dumpException($e);
    }
    $this->success(Lang::get('操作成功'));
  }

  public function system_email(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $system_email = Db::table('system_email')->find(1);
    $this->data['system_email'] = CommonService::objectToArray($system_email);
    return view('admin.admin.system_email',['data'=>$this->data]);
  }
  public function system_email_update(Request $request){
    if($this->admin_type=='distribution'){
      $this->error(Lang::get('您無法操作此項目'));
    }
    if(!$request->post('column')){ $this->error(Lang::get('資料不完整')); }

    $data = [
      $request->post('column') => $request->post('value')
    ];
    Db::table('system_email')->whereRaw("id=1")->update($data);
    $this->success(Lang::get('操作成功'));
  }
}