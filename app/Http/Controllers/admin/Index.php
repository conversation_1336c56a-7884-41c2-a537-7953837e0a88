<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Index extends MainController{
  const NONE_PRODUCT_FLAG = '0';

  public function __construct() {
    parent::__construct();
  }

  public function index(Request $request) {
    if( !empty($this->data['close_function']['首頁編輯']) ){
      $this->redirect(url('all/index'));
    }

    $index_online = Db::table('index_online')->find(1);
    $this->data['index_online'] = CommonService::objectToArray($index_online);

    $index_excel = Db::table('index_excel')->orderBy('id')->get();
    $this->data['index_excel'] = CommonService::objectToArray($index_excel);

    $slideshow = Db::table('slideshow')->orderBy('id')->get();
    $this->data['slideshow'] = CommonService::objectToArray($slideshow);

    $hot_product = Db::table('hot_product')->orderBy('id')->get();
    $this->data['hot_product'] = CommonService::objectToArray($hot_product);

    $recommend_product = Db::table('recommend_product')->orderBy('id')->get();
    $this->data['recommend_product'] = CommonService::objectToArray($recommend_product);

    $spe_price_product = Db::table('spe_price_product')->orderBy('id')->get();
    $this->data['spe_price_product'] = CommonService::objectToArray($spe_price_product);

    $expiring_product = Db::table('expiring_product')->orderBy('id')->get();	
    $this->data['expiring_product'] = CommonService::objectToArray($expiring_product);

    $time_product = Db::table('time_product')->orderBy('id')->get();		
    $this->data['time_product'] = CommonService::objectToArray($time_product);

    $product = Db::table('product')
                  ->select('id', 'title', 'ad_online as online')
                  ->whereRaw('distributor_id=0')
                  ->orderBy('order_id','asc')->get();
    $this->data['product'] = json_encode($product, JSON_UNESCAPED_UNICODE);	
    //var_dump( json_encode($product));

    return view('admin.index.index',['data'=> $this->data]);
  }

  /*iframe*/
  /*those method will cover old image by DB table id*/
  public function setSlideShow(Request $request) {
    $width = 1920;
    $height = 535;
    $DBTextConnecter = DBTextConnecter::withTableName('slideshow');
    $DBFileConnecter = new DBFileConnecter();
    try{
      for($i = 0; $i < 5; $i++){
        $updateData = [
          'id' => $i + 1,
          'title' => $request->post('title' . $i),
          'link' => $request->post('link' . $i),
          'online' => $request->post('online' . $i),
        ];
        $image = $request->file('image' . $i);
        if($image){
          $updateData['pic'] = $DBFileConnecter->fixedFileUp($image, 'slideshow'.$i, $width, $height);
        }
        $DBTextConnecter->setDataArray($updateData);
        $DBTextConnecter->upTextRow();
      }
      return '<h1>上傳成功</h1>';
    }catch (\Exception $e){
      return $e->getMessage();
    }
  }

  public function setPicWithLink(Request $request) {
    $image = $request->file('image');
    $width = $request->post('width');
    $height = $request->post('height');
    $link = $request->post('link');
    $id = $request->post('id');
    try{
      $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
      $DBFileConnecter = DBFileConnecter::withTableName('index_excel');
      $updateData = [
        'id' => $id,
        'data2' => $link
      ];
      /*
      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $updateData['data1'] = $DBFileConnecter->fixedFileUp($image, 'index_excel'.$id, $width, $height);
      }
      */
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      if($image){
        $DBFileConnecter->setFileArray([
          'data1' => $image
        ]);
        $DBFileConnecter->setPrivateKeyId($id);
        $DBFileConnecter->upFileRow();
      }
      ob_clean();
      echo('<h1>上傳成功</h1>');die();
    }catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  public function setthirteenth(Request $request) {
    $width = 1200; $height = 680;
    if($request->post('location') == 'left'){
      $dataId = [22, 24, 25];
    }else{
      $dataId = [23, 26, 27];
    }
    
    try{
      $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
      
      $updateData = [
        'id' => $dataId[0],
        'data2' => $request->post('link')
      ];
      $image = $request->file('image');		
      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $updateData['data1'] = $DBFileConnecter->fixedFileUp($image, 'index_excel'.$dataId[0], $width, $height);
      }
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();

      $updateData = [
        'id' => $dataId[1],
        'data3' => $request->post('title')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
    
      $updateData = [
        'id' => $dataId[2],
        'data3' => $request->post('content')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();

      ob_clean();
      echo('<h1>上傳成功</h1>');die();
    }catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  /*AJAX*/
  public function blockCtrl(Request $request) {
    try{
      $updateData = $request->post();
      $DBTextConnecter = DBTextConnecter::withTableName('index_online');
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function setSocialLink(Request $request) {
    try{
      $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
      $updateData = [
        'id' => 3,
        'data2' => $request->post('line')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 4,
        'data2' => $request->post('fb')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 5,
        'data2' => $request->post('email')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 6,
        'data2' => $request->post('phone')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 39,
        'data2' => $request->post('ig')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 40,
        'data2' => $request->post('tiktok')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 41,
        'data2' => $request->post('wechat')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $updateData = [
        'id' => 42,
        'data2' => $request->post('youtube')
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];

    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    
    return $outputData;
  }

  public function setProduct(Request $request) {
    $tableName = $request->post('tableName');
    $DBTextConnecter = DBTextConnecter::withTableName($tableName);
    try{
      for($i = 1; $i <= 10; $i++){
        $product_Number = $request->post('product_id'.$i);
        if($product_Number == self::NONE_PRODUCT_FLAG) {
          $product['id'] = 0;
        }else{
          $product = Db::table('productinfo')->where('id', $product_Number)->first();
          $product = CommonService::objectToArray($product);
        }
        $updateData = [
          'id' => $i,
          'product_id' => $product['id'] ?? 0,
        ];
        $DBTextConnecter->setDataArray($updateData);
        $DBTextConnecter->upTextRow();
      }
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function settext(Request $request) {
    $id = $request->post('id');
    $text = in_array($id, [38]) ?  $request->post('text') : nl2br($request->post('text'));
    $column = $request->post('column') ? $request->post('column') : "data3";
    /*$hasbr = $request->post('hasbr');if($hasbr){}*/
    $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
    try{
      $updateData = [
        'id' => $id,
        $column => $text
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function setLink(Request $request) {
    $text = $request->post('link');
    $id = $request->post('id');
    $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
    try{
      $updateData = [
        'id' => $id,
        'data2' => $text
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function setTimeRange(Request $request) {
    $start = strtotime($request->post('start'));
    $end = strtotime($request->post('end'));
    $DBTextConnecter = DBTextConnecter::withTableName('index_excel');
    try{
      $updateData = [
        'id' => 32,
        'data2' => $start,
        'data3' => $end
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function setProductOnline(Request $request) {
    $id = $request->post('id');
    $online = $request->post('online');
    $DBTextConnecter = DBTextConnecter::withTableName('product');
    try{
      $updateData = [
        'id' => $id,
        'ad_online' => $online
      ];
      $DBTextConnecter->setDataArray($updateData);
      $DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function setProductOrder(Request $request) {
    $idArray = json_decode($request->post('idArray'));
    $DBTextConnecter = DBTextConnecter::withTableName('product');
    try{
      foreach ($idArray as $key => $value) {
        $updateData = [
          'id' => $value,
          'order_id' => $key
        ];
        $DBTextConnecter->setDataArray($updateData);
        $DBTextConnecter->upTextRow();
      }
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }


  public function spePriceProduct(Request $request){
    $productInfo = Db::table('spe_price_product as sp')
                      ->select('productinfo.title', 'productinfo.id',
                          'productinfo.pic', 'productinfo.has_price', 'sp.orders', 'sp.id as sp_id')
                      ->whereRaw('productinfo.online = 1 AND sp.product_id <> 0')
                      ->orderByRaw('sp.orders asc, sp.id desc')
                      ->join('productinfo','productinfo.id','sp.product_id')
                      ->get();
    $productInfo = CommonService::objectToArray($productInfo);
    foreach ($productInfo as &$vo) {
      $vo['pic'] = json_decode($vo['pic'],true)[0];
    }
    // dump($productInfo);
    $this->data['productInfo'] = $productInfo;
    return view('admin.index.speprice',['data'=>$this->data]);
  }
  public function updateSpePrice(Request $request){
    // dump($request->post('data'));exit;
    $data = $request->post('data');
    foreach ($data as $key => $value) {
      try{
        if($value['del'] == 0){
          Db::table('spe_price_product')->where('id',$value['sp_id'])->update(['orders' => $value['orders']]);
        }elseif ($value['del'] == 1) {
          Db::table('spe_price_product')->where('id',$value['sp_id'])->delete();
        }
      }catch (\Exception $e){
        // $outputData = [
        // 	'status' => false,
        // 	'message' => $e->getMessage()
        // ];
        continue;
      }
    }

    $outputData = [
      'status' => true,
      'message' => 'success'
    ];
    return $outputData;
  }
}