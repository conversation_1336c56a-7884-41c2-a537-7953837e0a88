<?php

namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\PointRecords;
use App\Services\pattern\MemberInstance;

class Index extends MainController
{
    const PER_PAGE_ROWS = 20;
    const SIMPLE_MODE_PAGINATE = false;
    const ACCOUNT_MODE_ENUM = ['審核中', '通過', '黑名單', '停用'];
    const ORDER_STATE = [
        'New' => '新進訂單',
        'Complete' => '完成訂單',
        'Cancel' => '取消訂單',
        'Return' => '退貨訂單',
        'Pickable' => '待包裝訂單',
        'Picked' => '可寄出訂單',
    ];
    private $tableName;
    private $DBTextConnecter;

    public function __construct()
    {
        header("Access-Control-Allow-Origin: *");
        parent::__construct();
        $this->tableName = 'account';
        $this->DBTextConnecter = DBTextConnecter::withTableName('account', 'main_db');
    }
    private function accountDB()
    {
        return Db::connection('main_db')->table($this->tableName);
    }
    private function orderformDB()
    {
        return Db::connection('main_db')->table('orderform');
    }

    /*會員列表頁*/
    public function index(Request $request)
    {
        $status = $request->get('status') ?? '';
        $skip = $request->get('skip') ?? 'no';
        $search_result = MemberInstance::search_member($status, $request);
        $search_result['rowData'] = CommonService::objectToArray($search_result['rowData']);
        $this->data['tag'] = $search_result['tag'];
        $this->data['searchKey1'] = $search_result['searchKey1'];
        $this->data['searchKey2'] = $search_result['searchKey2'];
        $this->data['memberKey'] = $search_result['memberKey'];
        $this->data['nameKey'] = $search_result['nameKey'];
        $this->data['vipType'] = $search_result['vipType'];
        $this->data['partner_level_id'] = $search_result['partner_level_id'];
        $this->data['center_level_id'] = $search_result['center_level_id'];
        $this->data['userType'] = $search_result['userType'];
        $this->data['date_st'] = $search_result['date_st'];
        $this->data['date_en'] = $search_result['date_en'];
        $this->data['buy_date_st1'] = $search_result['buy_date_st1'];
        $this->data['buy_date_en1'] = $search_result['buy_date_en1'];
        $this->data['buy_date_st2'] = $search_result['buy_date_st2'];
        $this->data['buy_date_en2'] = $search_result['buy_date_en2'];
        $this->data['reg_date_st'] = $search_result['reg_date_st'];
        $this->data['reg_date_en'] = $search_result['reg_date_en'];

        $allMember = count($search_result['rowData']);
        $this->data['allMember'] = $allMember;

        $page = $request->get('page') ?? '1';
        $this->data['page'] = $page;
        $offset = 0 + (($page - 1) * self::PER_PAGE_ROWS);
        if ($search_result['tag'] == '1' && $search_result['nameKey'] != '') {
            //  已註冊/已購買的會員
            $rowDataItem = array_slice($search_result['h_number'], $offset, self::PER_PAGE_ROWS, true);
            $total_pages = floor(count($search_result['h_number']) / self::PER_PAGE_ROWS);
        }
        if ($search_result['tag'] == '2' && $search_result['nameKey'] != '') {
            //  未註冊/未購買的會員
            $rowDataItem = array_slice($search_result['n_number'], $offset, self::PER_PAGE_ROWS, true);
            $total_pages = floor(count($search_result['n_number']) / self::PER_PAGE_ROWS);
        }
        if ($search_result['nameKey'] == '') {
            //  總資料
            $rowDataItem = array_slice($search_result['rowData'], $offset, self::PER_PAGE_ROWS, true);
            $total_pages = floor($allMember / self::PER_PAGE_ROWS);
        }
        $total_pages += 1;
        $this->data['pages'] = range(1, $total_pages);
        $this->data['total_pages'] = $total_pages;

        $this->data['do_number'] = $search_result['do_number']; // 已註冊/已購買人數
        $do_percent = $search_result['do_number'] != 0 ? round(($search_result['do_number'] / $allMember) * 100, 2) . '%' : '0%';
        $this->data['do_percent'] = $do_percent; // 已註冊/已購買率

        if ($search_result['nameKey'] == "1") {
            $this->data['tag_name'] = ['', '已註冊', '未註冊'];
        }
        if ($search_result['nameKey'] == "2") {
            $this->data['tag_name'] = ['', '已購買', '未購買'];
        }
        //dump($rowDataItem);
        foreach ($rowDataItem as $key => $value) {
            if ($rowDataItem[$key]['status'] < count(self::ACCOUNT_MODE_ENUM) && $rowDataItem[$key]['status'] >= 0) {
                $rowDataItem[$key]['status'] = self::ACCOUNT_MODE_ENUM[$rowDataItem[$key]['status']];
            } else {
                $rowDataItem[$key]['status'] = "";
            }
            if ($skip == 'skip') { // 略過計算
                $rowDataItem[$key]['cancel'] = 0;
                $rowDataItem[$key]['return'] = 0;
                $rowDataItem[$key]['complete'] = 0;
                $rowDataItem[$key]['totalcom'] = 0;
                $rowDataItem[$key]['totaldel'] = 0;
            } else { // 處理計算
                $rowDataItem[$key]['cancel'] = $this->orderformDB()->where([
                    'user_id' => $rowDataItem[$key]['id'],
                    'status' => 'Cancel'
                ])->count();
                $rowDataItem[$key]['return'] = $this->orderformDB()->where([
                    'user_id' => $value['id'],
                    'status' => 'Return'
                ])->count();
                $rowDataItem[$key]['complete'] = $this->orderformDB()->where([
                    'user_id' => $rowDataItem[$key]['id'],
                    'status' => 'Complete'
                ])->count();
                $rowDataItem[$key]['totalcom'] = $this->orderformDB()->where([
                    'user_id' => $rowDataItem[$key]['id'],
                    'status' => 'Complete'
                ])->sum('total');
                $rowDataItem[$key]['totaldel'] = $this->orderformDB()->where([
                    'user_id' => $rowDataItem[$key]['id'],
                    'status' => 'Cancel',
                    'status' => 'Return'
                ])->sum('total');
            }
        }
        $this->data['rowDataItem'] = $rowDataItem;

        $this->data['Export'] = ['未匯出', '已匯出'];
        $this->data['Registered'] = ['未註冊', '已註冊'];
        $this->data['Buy'] = ['未購買', '已購買'];

        $Member_status = ['會員全部列表', '新進未開通', '黑名單列表', '停用名單列表'];
        $Member_status_tag = $status ? $Member_status[$status] : $Member_status['0'];
        $this->data['Member_status'] = $Member_status_tag;
        $this->data['urlstatus'] = $status;

        // VIP等級
        $vip_type = MemberInstance::get_vip_types()['db_data'];
        $this->data['vip_type'] =  CommonService::objectToArray($vip_type);

        $partner_levels = MemberInstance::get_partner_levels();
        $this->data['partner_levels'] =  $partner_levels['db_data'];
        $center_levels = MemberInstance::get_center_levels();
        $this->data['center_levels'] =  $center_levels['db_data'];

        return view('order.index.index', ['data' => $this->data]);
    }

    /*AJAX搜尋會員*/
    public function ajax_search(Request $request)
    {
        $search_result = MemberInstance::search_member($status = "", $request);
        if ($search_result['tag'] == '1' && $search_result['nameKey'] != '') {
            //  已註冊/已購買的會員
            $rowDataItem = $search_result['h_number'];
        }
        if ($search_result['tag'] == '2' && $search_result['nameKey'] != '') {
            //  未註冊/未購買的會員
            $rowDataItem = $search_result['n_number'];
        }
        if ($search_result['nameKey'] == '') {
            //  總資料
            $rowDataItem = $search_result['rowData'];
        }

        $search_result['rowData'] = $rowDataItem;
        return $search_result;
    }

    /*詳細內容頁面*/
    public function edit(Request $request)
    {
        $id = $request->get('id');
        /*會員資料*/
        $MemberInstance = new MemberInstance($id);
        $rowData = $MemberInstance->get_user_data();
        $obj_partner_level = MemberInstance::get_partner_levels([], true)['db_data'];
        $obj_center_level = MemberInstance::get_center_levels([], true)['db_data'];
        $rowData['partner_level_name'] = $obj_partner_level[$rowData['partner_level_id']]['name'] ?? '無';
        $rowData['center_level_name'] = $obj_center_level[$rowData['center_level_id']]['name'] ?? '無';
        dd($rowData);
        /*會員相關統計*/
        // $rowData['cancel'] = $MemberInstance->get_user_order_data(['status'=>'Cancel', 'method'=>'count']);
        // $rowData['return'] = $MemberInstance->get_user_order_data(['status'=>'Return', 'method'=>'count']);
        // $rowData['complete'] = $MemberInstance->get_user_order_data(['status'=>'Complete', 'method'=>'count']);
        // $rowData['totalcom'] = $MemberInstance->get_user_order_data(['status'=>'All', 'method'=>'sum']);
        // $rowData['totaldel'] = $MemberInstance->get_user_order_data(['status'=>'All_no', 'method'=>'sum']);

        /*推廣會員*/
        $request_obj = new Request([
            'upline_user' => $id,
        ]);
        $rowData['down_users'] = MemberInstance::search_member($status = "", $request_obj)['rowData'];

        /*訂單資料*/
        $rowData['order'] = $MemberInstance->get_user_order_data(['status' => 'All', 'method' => 'select']);
        foreach ($rowData['order'] as $key => &$value) {
            $value['subDepartment'] = substr($value['order_number'], 0, 1);
            $value['status'] = self::ORDER_STATE[$value['status']];
            $rowData['order'][$key] = $value;
        }

        $MemberInstance->change_user_id($rowData['upline_user']);
        $rowData['up_user'] = $MemberInstance->get_user_data();
        // dd($rowData);
        $this->data['rowData'] = $rowData;

        /*註冊商品資料*/
        $db_connect = CommonService::get_db_connect_by_lang_id();
        $reg_product = Db::connection($db_connect)->table('excel')->whereRaw("account_number = '" . $rowData['id'] . "'")->get();
        $this->data['reg_product'] = CommonService::objectToArray($reg_product);

        /*商品瀏覽設定*/
        $product_view = Db::connection('main_db')->table('product_view')->where('online', 1)->orderBy('id', 'desc')->get();
        $this->data['product_view'] = CommonService::objectToArray($product_view);

        return view('order.index.member-manager-info', ['data' => $this->data]);
    }

    /*單筆刪除*/
    public function delete(Request $request)
    {
        $id = $request->get('id');
        if ($id == config('extra.skychakra.member_system') || $id == config('extra.skychakra.member_month_divided')) {
            $this->error('此為系統帳戶，請勿刪除');
        }
        try {
            $this->accountDB()->delete($id);
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success(Lang::get('刪除成功'), '/order/index/index');
    }
    /*批次刪除*/
    public function multiDelete(Request $request)
    {
        $idList = $request->post('id');
        try {
            if ($idList) {
                $idList = json_decode($idList);
                if (
                    in_array(config('extra.skychakra.member_system'), $idList) ||
                    in_array(config('extra.skychakra.member_month_divided'), $idList)
                ) {
                    throw new \Exception(Lang::get('此為系統帳戶，請勿刪除'));
                }
                $this->accountDB()->whereIn('id', $idList)->delete();
            }
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success('刪除成功', url('Index/index'));
    }
    /*批次修改(VIP等級、會員狀態)*/
    public function multiupdate(Request $request)
    {
        $idList = $request->post('id');
        $column = $request->post('column');
        $value = $request->post('value');

        // 檢查欄位是否允許批次修改
        if (!in_array($column, ["vip_type", "partner_level_id", "center_level_id", "status"])) $this->error("此欄位不可批次修改");

        $MemberInstance = new MemberInstance(0);
        $arr_partner_levels = MemberInstance::get_partner_levels([], true)['db_data'];
        try {
            if ($idList) {
                $idList = json_decode($idList);
                foreach ($idList as $v) {
                    $MemberInstance->change_user_id($v);
                    $newData = null;
                    if ($column == "vip_type") {
                        // 修改vip_type並新增等級轉換紀錄
                        $MemberInstance->update_vip_type($value);
                    } else if ($column == "partner_level_id") {
                        $result = $MemberInstance->update_partner_level($value, true);
                        if ($result['msg']) {
                            throw new \Exception($result['msg']);
                        }
                        $contribution = $arr_partner_levels[$value]['contribution'] ?? 0;
                        $newData = ['partner_accumulation' => $contribution,]; /*重置累積投資金額到指定階級*/
                    } else if ($column == "center_level_id") {
                        $MemberInstance->update_user_data(['center_level_id' => $value]);
                    } else {
                        $newData[$column] = $value;
                    }
                    if ($newData) {
                        $newData['id'] = $v;
                        $this->DBTextConnecter->setDataArray($newData);
                        $this->DBTextConnecter->upTextRow();
                    }
                }
            }
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success(Lang::get('修改成功'), '/order/index/index');
    }

    /*匯出會員*/
    public function member_excel(Request $request)
    {
        $status = $request->get('status') ?? '';
        $search_result = MemberInstance::search_member($status, $request);
        if ($search_result['nameKey'] != '') { /*註冊、購買商品搜尋*/
            $memeber = $search_result['h_number'];
            $memeber_no = $search_result['n_number'];
        } else { /*會員搜尋*/
            //  總資料
            $memeber = $search_result['rowData'];
            $memeber_no = [];
        }

        foreach ($memeber as $key => $value) {
            //歷史購買定單編開始
            $memeber[$key]['order'] = $this->get_user_order($value['id']);
            //註冊商品編號開始
            $memeber[$key]['excel'] = $this->get_user_excel($value['id']);
        }
        foreach ($memeber_no as $key => $value) {
            //歷史購買定單編開始
            $memeber_no[$key]['order'] = $this->get_user_order($value['id']);
            //註冊商品編號開始
            $memeber_no[$key]['excel'] = $this->get_user_excel($value['id']);
        }
        // dump($memeber);dd($memeber_no);

        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();

        //建表（格式可以是2007（2010之後版本的EXCEL）、或5（支持95～2003））
        $objWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
        //輸出檔案

        // 設定工作表
        $objPHPExcel->setActiveSheetIndex(0);     //要使用的工作表
        $Sheet = $objPHPExcel->getActiveSheet();  //取得作用中的工作表
        switch ($search_result['nameKey']) {
            case "1":
                $Sheet->setTitle("已註冊");             //設定工作表名稱
                break;
            case "2":
                $Sheet->setTitle("已購買");             //設定工作表名稱
                break;
            default:
                $Sheet->setTitle("會員");               //設定工作表名稱
        }

        $objPHPExcel->createSheet();              //建立工作表
        //儲存格一般文字或數字欄位用setCellvalue即可
        $Sheet->setCellValue("A1", "此檔案【不可】用於匯入");
        $Sheet->setCellValue("A2", "會員編號");
        $Sheet->setCellValue("B2", "會員名稱");
        $Sheet->setCellValue("C2", "會員手機");
        $Sheet->setCellValue("D2", "來源類型");
        $Sheet->setCellValue("E2", "推薦者");
        $Sheet->setCellValue("F2", "會員級別");
        $Sheet->setCellValue("G2", "課程進度");
        $Sheet->setCellValue("H2", "合夥等級");
        $Sheet->setCellValue("I2", "中心等級");
        $Sheet->setCellValue("J2", "中心發起者");
        $Sheet->setCellValue("K2", "功德圓滿點數");
        $Sheet->setCellValue("L2", "消費圓滿點數");
        $Sheet->setCellValue("M2", "其他圓滿點數");
        $Sheet->setCellValue("N2", "增值積分");
        $Sheet->setCellValue("O2", "現金積分");
        $Sheet->setCellValue("P2", "會員信箱");
        $Sheet->setCellValue("Q2", "會員電話");
        $Sheet->setCellValue("R2", "會員地址");
        $Sheet->setCellValue("S2", "會員生日");

        $Sheet->setCellValue("T2", "歷史訂單編號");
        $Sheet->setCellValue("U2", "註冊商品編號");

        // 建立未註冊、未購買商品搜尋的工作表
        if ($search_result['nameKey'] != '') {
            $objPHPExcel->setActiveSheetIndex(1);       //要使用的工作表
            $Sheet_1 = $objPHPExcel->getActiveSheet();  //取得作用中的工作表
            switch ($search_result['nameKey']) {
                case "1":
                    $Sheet_1->setTitle("未註冊");             //設定工作表名稱
                    break;
                case "2":
                    $Sheet_1->setTitle("未購買");             //設定工作表名稱
                    break;
            }
            $objPHPExcel->createSheet();                //建立工作表

            //儲存格一般文字或數字欄位用setCellvalue即可
            $Sheet_1->setCellValue("A1", "此檔案【不可】用於匯入");
            $Sheet_1->setCellValue("A2", "會員編號");
            $Sheet_1->setCellValue("B2", "會員名稱");
            $Sheet_1->setCellValue("C2", "會員手機");
            $Sheet_1->setCellValue("D2", "來源類型");
            $Sheet_1->setCellValue("E2", "推薦者");
            $Sheet_1->setCellValue("F2", "會員級別");
            $Sheet_1->setCellValue("G2", "課程進度");
            $Sheet_1->setCellValue("H2", "合夥等級");
            $Sheet_1->setCellValue("I2", "中心等級");
            $Sheet_1->setCellValue("J2", "中心發起者");
            $Sheet_1->setCellValue("K2", "功德圓滿點數");
            $Sheet_1->setCellValue("L2", "消費圓滿點數");
            $Sheet_1->setCellValue("M2", "其他圓滿點數");
            $Sheet_1->setCellValue("N2", "增值積分");
            $Sheet_1->setCellValue("O2", "現金積分");
            $Sheet_1->setCellValue("P2", "會員信箱");
            $Sheet_1->setCellValue("Q2", "會員電話");
            $Sheet_1->setCellValue("R2", "會員地址");
            $Sheet_1->setCellValue("S2", "會員生日");

            $Sheet_1->setCellValue("T2", "歷史訂單編號");
            $Sheet_1->setCellValue("U2", "註冊商品編號");
        }

        // 設定excel內容
        $Sheet = $this->set_excel_member_content($Sheet, $memeber);
        if (isset($Sheet_1)) {
            $Sheet_1 = $this->set_excel_member_content($Sheet_1, $memeber_no);
        }
        //輸出Excel檔
        header('Content-Type: application/vnd.ms-excel');
        //下載檔案名字跟類型
        header('Content-Disposition: attachment;filename=會員資料匯出.xlsx');
        //禁止緩存每次都是新下載
        header('Cache-Control: max-age=0');
        $objWriter->save('php://output');
        //echo "<script>alert('匯出資料完成'); location.href = '".url('Index/index')."';</script>";
        //$objWriter->save('test.xlsx');
    }

    /*取得使用者訂單編號(組成文字)*/
    private function get_user_order($user_id)
    {
        $MemberInstance = new MemberInstance($user_id);
        $orderform = $MemberInstance->get_user_order_data(['status' => 'All', 'method' => 'select']);

        $order_list = '';
        foreach ($orderform as $order_key => $order_value) {
            $order_list .= $orderform[$order_key]['order_number'];
            if (!empty($orderform[$order_key + 1]['order_number']))
                $order_list .= '，';
        }
        return $order_list;
    }
    /*取得使用者註冊商品機身碼(組成文字)*/
    private function get_user_excel($user_id)
    {
        $db_connect = CommonService::get_db_connect_by_lang_id();
        $excel = Db::table('excel')->whereRaw("account_number = '" . $user_id . "'")->orderByRaw('id desc')->select('product_code')->get();
        $product_code_list = '';
        foreach ($excel as $order_key => $order_value) {
            $product_code_list .= $excel[$order_key]['product_code'];

            if (!empty($excel[$order_key + 1]['product_code']))
                $product_code_list .= '，';
        }
        return $product_code_list;
    }
    /*設定會員匯出excel內容*/
    private function set_excel_member_content($target_sheet, $memeber)
    {
        $db_id_to_user_number = $this->accountDB()->pluck('number', 'id')->toArray();
        $arr_registration_from = MemberInstance::get_registration_from([], true)['db_data'];
        $arr_vip_types = MemberInstance::get_vip_types([], true)['db_data'];
        $arr_partner_levels = MemberInstance::get_partner_levels([], true)['db_data'];
        $arr_center_levels = MemberInstance::get_center_levels([], true)['db_data'];

        $db_connect = CommonService::get_db_connect_by_lang_id();
        foreach ($memeber as $key => $value) {
            $target_index = $key + 3;

            $this->accountDB()->where('id', $value['id'])->update(['export' => 1]);

            //修改地址格式
            try {
                if ($value['home']) {
                    $addcods = explode('|||', $value['home']);

                    $city = Db::connection($db_connect)->table('city')->whereRaw("AutoNo = '" . $addcods[0] . "'")->select('Name')->first();
                    $town = Db::connection($db_connect)->table('town')->whereRaw("AutoNo = '" . $addcods[1] . "'")->select('Name')->first();
                    $post = $addcods[2];
                    $otheradd = $addcods[3];

                    $value['home'] = $post . ' ';
                    if (empty($city) == false) {
                        $value['home'] .= $city->Name;
                    }
                    if (empty($town) == false) {
                        $value['home'] .= $town->Name;
                    }
                    $value['home'] .= $otheradd;
                }
            } catch (\Exception $e) {
                $value['home'] = $value['home'];
            }

            $target_sheet->setCellValue("A" . $target_index, '="' . $value['number'] . '"');
            $target_sheet->setCellValue("B" . $target_index, '="' . $value['name'] . '"');
            $target_sheet->setCellValue("C" . $target_index, '="' . $value['phone'] . '"');
            $target_sheet->setCellValue("D" . $target_index, '="' . ($arr_registration_from[$value['registration_from']]['name'] ?? '') . '"');
            $target_sheet->setCellValue("E" . $target_index, '="' . ($db_id_to_user_number[$value['upline_user']] ?? '') . '"');
            $target_sheet->setCellValue("F" . $target_index, '="' . ($arr_vip_types[$value['vip_type']]['vip_name'] ?? '') . '"');
            $target_sheet->setCellValue("G" . $target_index, '="' . ($arr_vip_types[$value['vip_type_course']]['vip_name'] ?? '') . '"');
            $target_sheet->setCellValue("H" . $target_index, '="' . ($arr_partner_levels[$value['partner_level_id']]['name'] ?? '') . '"');
            $target_sheet->setCellValue("I" . $target_index, '="' . ($arr_center_levels[$value['center_level_id']]['name'] ?? '') . '"');
            $target_sheet->setCellValue("J" . $target_index, '="' . ($db_id_to_user_number[$value['center_raiser_id']] ?? '') . '"');
            $target_sheet->setCellValue("K" . $target_index, $value['increasing_limit_invest']);
            $target_sheet->setCellValue("L" . $target_index, $value['increasing_limit_consumption']);
            $target_sheet->setCellValue("M" . $target_index, $value['increasing_limit_other']);
            $target_sheet->setCellValue("N" . $target_index, $value['point_increasable']);
            $target_sheet->setCellValue("O" . $target_index, $value['point']);
            $target_sheet->setCellValue("P" . $target_index, '="' . $value['email'] . '"');
            $target_sheet->setCellValue("Q" . $target_index, '="' . $value['tele'] . '"');
            $target_sheet->setCellValue("R" . $target_index, '="' . $value['home'] . '"');
            if (!empty($value['birthday'])) {
                $target_sheet->setCellValue("S" . $target_index, '="' . date('Y-m-d', $value['birthday']) . '"');
            }

            $target_sheet->setCellValue("T" . $target_index, '="' . $value['order'] . '"');
            $target_sheet->setCellValue("U" . $target_index, '="' . $value['excel'] . '"');
        }

        //調整欄寬
        $target_sheet->getColumnDimension('A')->setWidth(20);
        $target_sheet->getColumnDimension('B')->setWidth(20);
        $target_sheet->getColumnDimension('C')->setWidth(20);
        $target_sheet->getColumnDimension('D')->setWidth(20);
        $target_sheet->getColumnDimension('E')->setWidth(20);
        $target_sheet->getColumnDimension('F')->setWidth(20);
        $target_sheet->getColumnDimension('G')->setWidth(20);
        $target_sheet->getColumnDimension('H')->setWidth(20);
        $target_sheet->getColumnDimension('I')->setWidth(20);
        $target_sheet->getColumnDimension('J')->setWidth(20);
        $target_sheet->getColumnDimension('K')->setWidth(20);
        $target_sheet->getColumnDimension('L')->setWidth(20);
        $target_sheet->getColumnDimension('M')->setWidth(20);
        $target_sheet->getColumnDimension('N')->setWidth(20);
        $target_sheet->getColumnDimension('O')->setWidth(20);
        $target_sheet->getColumnDimension('P')->setWidth(40);
        $target_sheet->getColumnDimension('Q')->setWidth(20);
        $target_sheet->getColumnDimension('R')->setWidth(40);
        $target_sheet->getColumnDimension('S')->setWidth(20);
        $target_sheet->getColumnDimension('T')->setWidth(60);
        $target_sheet->getColumnDimension('U')->setWidth(60);

        return $target_sheet;
    }

    /*會員匯入*/
    public function import(Request $request)
    {
        //接收檔案
        $files = $request->file("file");
        $type = explode(".", $request->file("file")->getClientOriginalName());

        if (!$type[1] == 'xlsx') {
            $this->error("格式錯誤，請上傳Excel檔");
        }
        //儲存檔案
        $info = $files->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel');
        //檔案路徑
        $filename = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel' . DS . $info->getFilename();

        $PHPReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xlsx");
        $PHPExcel = $PHPReader->load($filename);
        $sheet = $PHPExcel->getSheet(0);
        $allRow = $sheet->getHighestRow(); //取得最大的行號
        $allColumn = $sheet->getHighestColumn(); //取得最大的列號
        if ($allRow > 30000) {
            $this->error('匯入資料超過3萬筆，請分批操作');
        }

        /*整理excel資料*/
        $excel_data = [];
        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
            $data = [];
            $data['number'] = trim($PHPExcel->getActiveSheet()->getCell("A" . $currentRow)->getCalculatedValue());
            $data['name'] = trim($PHPExcel->getActiveSheet()->getCell("B" . $currentRow)->getCalculatedValue());
            $data['upline_user'] = trim($PHPExcel->getActiveSheet()->getCell("C" . $currentRow)->getCalculatedValue());
            $data['phone'] = trim($PHPExcel->getActiveSheet()->getCell("D" . $currentRow)->getCalculatedValue());
            $data['email'] = trim($PHPExcel->getActiveSheet()->getCell("E" . $currentRow)->getCalculatedValue());
            $data['tele'] = trim($PHPExcel->getActiveSheet()->getCell("F" . $currentRow)->getCalculatedValue());
            $data['home'] = trim($PHPExcel->getActiveSheet()->getCell("G" . $currentRow)->getCalculatedValue());
            $data['registration_from'] = trim($PHPExcel->getActiveSheet()->getCell("H" . $currentRow)->getCalculatedValue());
            $data['vip_type'] = trim($PHPExcel->getActiveSheet()->getCell("I" . $currentRow)->getCalculatedValue());
            $data['vip_type_course'] = trim($PHPExcel->getActiveSheet()->getCell("J" . $currentRow)->getCalculatedValue());
            $data['center_level_id'] = trim($PHPExcel->getActiveSheet()->getCell("K" . $currentRow)->getCalculatedValue());
            $data['center_raiser_id'] = trim($PHPExcel->getActiveSheet()->getCell("L" . $currentRow)->getCalculatedValue());
            $data['partner_level_id'] = trim($PHPExcel->getActiveSheet()->getCell("M" . $currentRow)->getCalculatedValue());
            $data['partner_accumulation'] = trim($PHPExcel->getActiveSheet()->getCell("N" . $currentRow)->getCalculatedValue());
            if (!$data['number'] && !$data['phone']) {
                break;
            }
            array_push($excel_data, $data);
        }

        /*批次匯入*/
        $MemberInstance = new MemberInstance(0);
        $array_error_data = $MemberInstance->import_member($excel_data);
        if (count($array_error_data) > 0) {
            foreach ($array_error_data as $key => $item) {
                $array_error_data[$key] = "<small>帳號(" . $item['data']['number'] . "/" . $item['data']['phone'] . ")：" . $item['error_msg'] . "</small>";
            }
            $this->error('匯入失敗', $url = null, implode("<br>", $array_error_data), $wait = -1);
        }

        $this->success('匯入成功');
    }

    /*贈送/收回現金積分*/
    public function gift(Request $request)
    {
        $number = $request->post('number');
        $member = $this->accountDB()->where('number', $number)->first();
        if (!$member) {
            $this->error('會員不存在');
        }
        if (!$request->post('point')) {
            $this->error('未設定數量');
        }

        try {
            Db::beginTransaction();
            $PointRecords = new PointRecords($member->id);
            $records = $PointRecords->add_records([
                'msg'            => $request->post('msg') ?? '管理員調整',
                'points'        => $request->post('point'),
                'belongs_time'    => time()
            ]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('操作成功');
    }
    /*贈送/收回圓滿點數*/
    public function gift_limit(Request $request)
    {
        $number = $request->post('number');
        $member = $this->accountDB()->where('number', $number)->first();
        if (!$member) {
            $this->error('會員不存在');
        }
        if (!$request->post('limit_type')) {
            $this->error('未設定類型');
        }
        if (!$request->post('point')) {
            $this->error('未設定數量');
        }

        try {
            $MemberInstance = new MemberInstance($member->id);
            $MemberInstance->add_increasing_limit_record(
                $request->post('point'),
                $request->post('msg') ?? '管理員調整',
                $request->post('limit_type'),
                5
            );
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('操作成功');
    }


    /*添加會員(單個)*/
    public function addMember(Request $request)
    {
        $id = 0;
        $post = $request->post();
        $newData = [
            'upline_user' => $post['upline_user'] ?? '',
            'phone' => $post['phone'] ?? '',
            'pwd' => $post['pwd'] ?? '',
            'name' => $post['name'] ?? '',
            'birthday' => $post['birthday'] ?? '',
            'email' => $post['email'] ?? '',
            'tele' => $post['tele'] ?? '',
            'F_S_NH_Address' => $post['F_S_NH_Address'] ?? '',
        ];
        $newData['status'] = 1;

        /*新增會員*/
        $MemberInstance = new MemberInstance(0);
        $returnData = $MemberInstance->insert_user_data($newData);
        if ($returnData['code'] == 0) {
            $this->error($returnData['msg']);
        }

        $this->success(Lang::get('新增成功'));
    }
    /*修改會員密碼*/
    public function changePassword(Request $request)
    {
        $id = $request->post('id');
        $data = [
            'password' => $request->post('password')
        ];

        /*修改會員*/
        $MemberInstance = new MemberInstance($id);
        $returnData = $MemberInstance->update_user_data($data);
        if ($returnData['code'] == 0) {
            $this->error($returnData['msg']);
        }

        $this->success(Lang::get('修改成功'));
    }
    /*修改會員資料(密碼外)*/
    public function updateMember(Request $request)
    {
        $id = $request->post('id');
        if (!$id) {
            $this->error('請提供編輯對象');
        }

        $updateData = $request->post();

        /*修改會員*/
        $MemberInstance = new MemberInstance($id);
        $returnData = $MemberInstance->update_user_data($updateData);
        if ($returnData['code'] == 0) {
            $this->error($returnData['msg']);
        }

        $this->success(Lang::get('修改成功'));
    }
}
