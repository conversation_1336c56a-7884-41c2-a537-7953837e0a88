<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class Member extends MainController{
  private $tableName;
  private $associated_tableName;
  private $associated_column;
  private $DBTextConnecter;

  public function __construct() {
    parent::__construct();

    $this->tableName = 'product_view';
    $this->associated_tableName = 'product_view_product';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->tableName, 'main_db');
    $this->associated_column = 'view_id';
  }

  public function product_view(Request $request){
    $lang = DB::connection('main_db')->table('lang')->where('lang_status', 1)->get();
    $this->data['lang'] = CommonService::objectToArray($lang);

    return view('order.member.product_view',['data'=>$this->data]);
  }
  /*列表頁相關功能api*/
  public function getActList(Request $request){
    $searchKey = $request->post('searchKey');

    // var_dump($searchKey);
    if(!empty($searchKey)){
      $list = DB::connection('main_db')->table($this->tableName)
                                      ->where(function($query) use ($searchKey){
                                        $query->orWhere('name', 'like', '%'.$searchKey.'%');
                                      })
                                      ->orderBy('id', 'desc')->get();
      $search = "> 搜尋：".$searchKey;
    }else{
      $list = DB::connection('main_db')->table($this->tableName)->orderBy('id', 'desc')->get();
      $search = "";
    }

    $retData = ['actList' => $list, 'search'=>$search];
    return $retData;
  }
  public function changeOnline(Request $request){
    $itemData = $request->post();
    $online =  ($itemData['online'] == 'true') ? 1 : 0;

    DB::connection('main_db')->table($this->tableName)->where('id',$itemData['id'])->update(['online' => $online]);

    $this->success("操作成功");
  }
  public function delAct(Request $request){
    $itemData = $request->post();
    if($itemData['id']==1){
      $this->error("無法刪除系統預設等級");
    }

    $delId = DB::connection('main_db')->table($this->tableName)->where('id',$itemData['id'])->delete();

    if($this->associated_column){
      $delProd = DB::connection('main_db')->table($this->associated_tableName)->where($this->associated_column,$itemData['id'])->delete();
    }

    $this->success("操作成功");
  }
  public function doCreate(Request $request) {
    // dump($request->post());
    $newData['name']      = $request->post('name');
    $newData['online']    = 1;

    // dump($newData);exit;
    $this->DBTextConnecter->setDataArray($newData);
    $new_id = $this->DBTextConnecter->createTextRow();
    $this->success('新增成功', url("order/Member/product_view_edit").'?id='.$new_id);
  }
  public function product_view_edit(Request $request) {
    $id = $request->get('id');
    $singleData = DB::connection('main_db')->table($this->tableName)->find($id);
    $this->data['singleData'] = CommonService::objectToArray($singleData);
    $this->data['actId'] = $id;

    $langId = $request->get('langId') ?? config('extra.shop.LangId');
    $this->data['langId'] = $langId;

    return view('order.member.product_view_edit',['data'=>$this->data]);
  }
  /*更新活動資料*/
  public function update(Request $request) {
    try{
      $newData = [
        'id' => $request->post('id'),
        'name' => $request->post('name'),
        // 'online' => 1
      ];
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();
    }
    catch (\Exception $e) {
      $this->dumpException($e);
    }
    $this->success('更新成功');
  }
  /*讀取活動資料及已套用商品*/
  public function getActProd(Request $request){
    $postData = $request->post();
    $actId = $postData['actId'];
    $actInfo = DB::connection('main_db')->table($this->tableName)->where('id',$actId)->first();
    $langId = $postData['langId'] ?? config('extra.shop.langId');
    $lang = DB::connection('main_db')->table('lang')->where('lang_id',$langId)->first();
    $db_connect = CommonService::get_db_connect_by_lang_id($langId);

    $actProd = [];
    $actProd_ids = DB::connection('main_db')->table($this->associated_tableName)
                      ->where($this->associated_column, $actId)
                      ->where('lang_id', $langId)->get();
    $actProd_ids = CommonService::objectToArray($actProd_ids);
    $in_prods = [];
    foreach ($actProd_ids as $key => $value) {
      array_push($in_prods, $value['prod_id']);
    }
    if($in_prods){
      $actProd = DB::connection($db_connect)->table('productinfo')->whereRaw('id in ('.implode(',', $in_prods).')')->get();
      $actProd = CommonService::objectToArray($actProd);
      foreach($actProd as $k => $v){
        $pic1 = json_decode($v['pic'],true);
        $actProd[$k]['pic1'] = $pic1[0];
      }
    }

    $retData = [
      'status'  => 200,
      'actInfo' => $actInfo,
      'actProd' => $actProd,
      'lang'	  => $lang,
    ];
    return $retData;
  }
  // 活動、折扣取得商品
  public function getCateProd(Request $request){
    $id = $request->post('cateId');
    $first = $request->post('first');

    $actId = $request->post('actId');
    $langId = $request->post('langId') ?? config('extra.shop.langId');
    $db_connect = CommonService::get_db_connect_by_lang_id($langId);
    if($first){
      //是第一階
      $productinfo = DB::connection($db_connect)->table('productinfo as pi')
                                                ->whereRaw("final_array like '%\"prev_id\":\"".$id."\"%\"parent_id\":\"0\"%'")
                                                ->orderByRaw('pi.order_id, pi.id desc')->get();			
    }else{
      $productinfo = DB::connection($db_connect)->table('productinfo as pi')
                                                ->whereRaw("final_array like '%\"parent_id\":\"".$id."\"%'")
                                                ->orderByRaw('pi.order_id, pi.id desc')->get();
    }
    $productinfo = CommonService::objectToArray($productinfo);

    if(empty($productinfo)){
      $productinfo = DB::connection($db_connect)->table('productinfo as pi')
              ->whereRaw("final_array like '%\"branch_id\": \"".$id."\"%' ")
              ->orderByRaw('pi.order_id, pi.id desc')
              ->get();
      $productinfo = CommonService::objectToArray($productinfo);	
    }
    $productinfo = array_filter($productinfo, function($value)use($actId, $langId){
      $repeat = DB::connection('main_db')->table($this->associated_tableName)
                                        ->where($this->associated_column, $actId)
                                        ->where('lang_id', $langId)
                                        ->where('prod_id', $value['id'])
                                        ->first();
      return !$repeat;
    });
    foreach($productinfo as $k => $v){
      $pic1 = json_decode($v['pic'],true);
      $productinfo[$k]['pic1'] = $pic1[0];
    }
      
    //echo DB::connection($db_connect)->table('productinfo')->getLastSql();
    $data = ['cateId' => $id,'productinfo' => $productinfo];
    return $data;
  }
  /*加入活動商品*/
  public function insertAct(Request $request){
    $postData   = $request->post();
    $actData    = $postData['actData'] ?? '[]';
    $actData    = json_decode($actData, true);
    $actId      = $postData['actId'];
    $langId     = $postData['langId'] ?? config('extra.shop.langId');

    $result = $this->add_act_prouduct($actId, $actData, $langId);
    $echoData = [
      'status'    => 200,
      'actData'   => $result['actData'],
      'actProd'   => $result['actProd'],
    ];
    $this->success('新增完成');
  }
  private function add_act_prouduct($actId, $actData, $langId){
    if(!$this->associated_column){ return ['actData'=>[], 'actProd'=>[]]; }

    $langId = $langId ?? config('extra.shop.langId');
    $db_connect = CommonService::get_db_connect_by_lang_id($langId);

    $createSeries   = false;
    $createCate     = false;
    $actSeries      = [];
    $actCate        = [];
    $actProd        = [];
    $series = isset($actData['series']) ? $actData['series'] : [];
    foreach($series as $serKey => $serValue){
      if (isset($serValue['select'])){
        if($serValue['select'] == 'true'){
          $createSeries = true;
          $seriesProd = DB::connection($db_connect)->table('product')->select('pi.id')->alias('prod')
                                                  ->join('typeinfo ti','prod.id = ti.parent_id')
                                                  ->join('productinfo pi','ti.id = pi.parent_id')
                                                  ->where('prod.id',$serValue['id'])
                                                  ->get();
          foreach($seriesProd as $spKey => $spValue){
            $repeat = DB::connection('main_db')->table($this->associated_tableName)
                                              ->whereRaw($this->associated_column, $actId)
                                              ->where('lang_id', $langId)
                                              ->where('prod_id', $spValue['id'])
                                              ->first();
            if(!$repeat){
              $actSeries[] = $spValue;
            }
          }
        }//if
      }//if
    }//foreach

    $cate = isset($actData['cate']) ? $actData['cate'] : [];
    foreach($cate as $cateKey => $cateValue){
      if (isset($cateValue['select'])){
        if($cateValue['select'] == 'true'){
          $createCate = true;
          $cateProd = DB::connection($db_connect)->table('typeinfo')->alias('ti')
                                                ->join('productinfo pi','ti.id = pi.parent_id')
                                                ->where('ti.id',$cateValue['id'])
                                                ->get();
          // echo $sql = DB::connection($db_connect)->table('typeinfo')->getLastSql();
          foreach($cateProd as $cpKey => $cpValue){
            $repeat = DB::connection('main_db')->table($this->associated_tableName)
                                              ->whereRaw($this->associated_column, $actId)
                                              ->where('lang_id', $langId)
                                              ->where('prod_id', $cpValue['id'])
                                              ->first();
            if(!$repeat){
              $actCate[] = $cpValue;
            }
          }
        }//if
      }//if
    }//foreach

    if ($createSeries){
      foreach ($actSeries as $asKey => $asValue){
        $actProd[] = [
          $this->associated_column    => $actId,
          'lang_id'	=> $langId,
          'prod_id'   => $asValue['id']
        ];
      }//foreach
    }elseif($createCate){
      foreach ($actCate as $acKey => $acValue){
        $actProd[] = [
          $this->associated_column    => $actId,
          'lang_id'	=> $langId,
          'prod_id'   => $acValue['id']
        ];
      }//foreach
    }elseif(isset($actData['cateProd'])){
      foreach ($actData['cateProd'] as $cateProdKey => $cateProdValue){
        if (isset($cateProdValue['select'])){
          if($cateProdValue['select'] == 'true'){
            $actProd[] = [
              $this->associated_column    => $actId,
              'lang_id'	=> $langId,
              'prod_id'   => $cateProdValue['id']
            ];
          }//if
        }//if
      }//foreach
    }//else

    // dump($actProd);exit;
    DB::connection('main_db')->table($this->associated_tableName)->insert($actProd);

    return ['actData' => $actData, 'actProd' => $actProd];
  }
  /*刪除活動商品*/
  public function delActProd(Request $request){
    if(!$this->associated_column){ return ['status'=>500, 'delProd'=>[]]; }

    $postData = $request->post();
    $actId = $postData['actId'];
    $langId = $postData['langId'] ?? config('extra.shop.langId');

    $delProd    = $postData['cateProd'] ?? '[]';
    $delProd    = json_decode($delProd, true);
    foreach($delProd as $dpKey => $dpValue){
      if (isset($dpValue['select'])) {
        if ($dpValue['select'] == 'true') {
          DB::connection('main_db')->table($this->associated_tableName)
                                  ->where($this->associated_column, $actId)
                                  ->where('prod_id',$dpValue['id'])
                                  ->where('lang_id', $langId)
                                  ->delete();
        }
      }
    }

    $retData = [
      'status'  => 200,
      'delProd' => $delProd,
    ];
    $this->success('刪除完成');
  }
}