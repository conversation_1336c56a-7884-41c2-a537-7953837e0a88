/*側邊按鈕*/
.social-bar{
    width: 50px;
}
.social-bar__item{
    width: 65px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}
#menu-link:checked~ .check-menu .hb{
    right: 50px;
}
#menu-link.active~.fixed-menu, #menu-link:checked~.fixed-menu{
    left: 0;
}
.social-bar__line {
    background-color: #39cd00;
}
.social-bar__tiktok {
    background: linear-gradient(135deg, rgb(107, 192, 203) 25%, rgb(223, 50, 94) 80%);
}
.social-bar__wechat {
    background-color: #2dc100;
}
.social-bar__youtube {
    background-color: #ff0000;
}
.social-bar__fb {
    background-color: #2a5297;
}


/*商品選單*/
.course_block .cate_title {
    font-size: 1.2rem;
    color: #2a2a2a;
    margin-bottom: 1rem;
    font-weight: 500;
    line-height: 1.2;
}
.course_block .cate_title span {
    display: block;
    color: #01B3D7;
    font-size: 2rem;
}
.course_block .cardBox_area:after {
    content: "";
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, rgb(1, 179, 215) 0%, rgb(1, 179, 215) 40%, rgba(1, 179, 215, 0) 100%);
    position: absolute;
    left: 0;
    top: 10px;
}
.productPublic .cardBox-header a>div{
    color: #2a2a2a;
    /* padding: 0.8rem 1.5rem; */
    display: block;
}
.slideLink a.active {
    color: #01B3D7;
}

/* 商品banner */
.content_area .page-banner .page-title{
    color: var(--white);
    line-height: 1.5;
    font-weight: 600;
    font-size: 2rem;
    font-family: "noto sans tc", "Arial";
    /* position: relative; */
    padding: 0;
}
.content_area .page-banner .page-title::after{
    content: "";
    width: 50px;
    height: 3px;
    background-color: #01B3D7;
    position: absolute;
    left: 0;
    bottom: -0.5rem;
}
@media (min-width: 1200px) {
    .content_area .page-banner .page-title{
        font-size: 2.4rem;
    }
}

/* 商品列表 */
.proBox .item .img{
    border-radius: 10px;
}

/* 商品詳細內容 */
.container.max-wideVersion.productPublic.productinfo{
    padding: 3rem 0px;
}
.productIntroBox .pdSpacing .prod_box_shadow{
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 1rem;
}
.proTitleBox h3{
    color: #2a2a2a;
    font-family: "noto sans tc", "Arial";
    font-size: 2rem;
    margin-bottom: 1rem;
}
.productIntroBox .popularProBranch{
    border-top: 0;
}
ul.tabNavBox{
    margin-bottom: .25rem !important;
}