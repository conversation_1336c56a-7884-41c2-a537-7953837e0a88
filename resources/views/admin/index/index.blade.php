@extends('admin.Public.aside')

@section('title')後台@endsection

@section('cssChange')
    <link rel="stylesheet" href="{{__PUBLIC__}}/css/style_admin_index.css?110">
    <style>
        .infobox label { width: 80px; }
        .infobox input, .infobox textarea { width: calc(100% - 80px); }
    </style>
@endsection

@section('content')
    <div id="content">
        <iframe id="changeImageFormIframe" name="changeImageFormIframe" style="display: none;"></iframe>
        <iframe id="changeGroupFormIframe" name="changeGroupFormIframe" style="display: none;"></iframe>
        <iframe id="changeImgtextFormIframe" name="changeImgtextFormIframe" style="display: none;"></iframe>
        <p  class="text-center text-danger">* 請注意圖片過大時會無法自動切割</p>

        <!-- 更換一張圖片 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeImageBox" >
            跳出視窗
        </button>
        <div class="modal main-modal fade" id="changeImageBox" tabindex="-1" role="dialog" aria-labelledby="changeImageBoxLabel" aria-hidden="true" v-if="visibility">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" @click="setHidden">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    
                    <div class="modal-body">
                        <form name="changeImageBoxForm" :action="action" method="post" target="changeImageFormIframe" enctype="multipart/form-data" class="p-3">
                            @csrf
                            <input type='hidden' name="_token" value="{{csrf_token()}}">
                            <div class="image-box">
                                <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="previewImg">
                                <img style="max-width:100%; max-height:100%;" class="preview" :src="src"/>
                            </div>
                            <input type="hidden" :value="width" name="width">
                            <input type="hidden" :value="height" name="height">
                            <input type="hidden" :value="id" name="id">
                            <p v-text="'建議大小：' + width + '*' + height"></p>
                            <div class="infobox">
                                <label>URL：</label><input name="link" v-model="link" type="text">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="sendbtn btn" @click="formSubmit">儲存</button></button>
                    </div>
                </div>
            </div>
        </div>  

        <!-- 更換五張圖片 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeGroupBox">
            跳出視窗
        </button>
        <!-- Modal -->
        <div class="modal main-modal fade" id="changeGroupBox" tabindex="-1" role="dialog" aria-labelledby="changeGroupBoxLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-body">
                    <form name="changeGroupBoxForm" :action="action" method="post" target="changeGroupFormIframe" enctype="multipart/form-data">
                        @csrf
                        <div id="change-group-box" class="changeInfo" v-if="visibility">
                            <div class="content_area_img">
                                <div class="image-box">
                                    <input type='file' class="upl1" accept="image/*"
                                        v-for="(slide, index) in slideshow" :ref="'img' + index"
                                        :name="'image' + index" v-show="src == slide.src" @change="previewImg(index)">
                                    <img :src="src" class="preview preImg"/>
                                </div>
                                <div class="sm-group-box w-100">
                                    <div class="sm-image-box" 
                                        v-for="(slide, index) in slideshow" @mouseenter="switchSrc(index)">
                                        <img :src="slide.src" :class="['preview', slide.online==0 ? 'opacity_05' : '']"/>
                                    </div>
                                </div>
                                <p class="w-100">建議大小：1920*535</p>
                                
                                <div v-for="(slide, index) in slideshow" v-show="src == slide.src" class="w-100">
                                    <div class="infobox">
                                        <div class="item">
                                            <label>狀態：</label>
                                            <select :name="'online' + index" v-model="slide.online" style="width: calc(100% - 50px);">
                                                <option value="1">顯示</option>
                                                <option value="0">隱藏</option>
                                            </select>
                                        </div>
                                        <div class="item">
                                            <label>名稱：</label><input :name="'title' + index" v-model="slide.title" type="text">
                                        </div>
                                        <div class="item">
                                            <label>URL：</label><input :name="'link' + index" v-model="slide.link" type="text">
                                        </div>
                                        
                                    </div>
                                
                                </div>
                            </div>
                            
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="sendbtn btn" @click="formSubmit">儲存</button>
                </div>
              </div>
            </div>
          </div>

        <!-- <div id="changeGroupBox">
            <form name="changeGroupBoxForm" :action="action" method="post" target="changeGroupFormIframe" enctype="multipart/form-data">
                @csrf
                <div id="change-group-box" class="changeInfo" v-if="visibility">
                    <div class="changebody">
                        <span class="bi bi-x-circle-fill" @click="setHidden"></span>
                        <div class="content_area_img">
                            <div class="image-box">
                                <input type='file' class="upl1" accept="image/*"
                                    v-for="(slide, index) in slideshow" :ref="'img' + index"
                                    :name="'image' + index" v-show="src == slide.src" @change="previewImg(index)">
                                <img :src="src" class="preview preImg"/>
                            </div>
                            <div class="sm-group-box w-100">
                                <div class="sm-image-box" 
                                    v-for="(slide, index) in slideshow" @mouseenter="switchSrc(index)">
                                    <img :src="slide.src" :class="['preview', slide.online==0 ? 'opacity_05' : '']"/>
                                </div>
                            </div>
                            <p class="w-100">建議大小：1920*535</p>
                            <div v-for="(slide, index) in slideshow" v-show="src == slide.src" class="w-100">
                                <div class="infobox">
                                    <div class="item">
                                        <label>狀態：</label>
                                        <select :name="'online' + index" v-model="slide.online" style="width: calc(100% - 50px);">
                                            <option value="1">顯示</option>
                                            <option value="0">隱藏</option>
                                        </select>
                                    </div>
                                    <div class="item">
                                        <label>名稱：</label><input :name="'title' + index" v-model="slide.title" type="text">
                                    </div>
                                    <div class="item">
                                        <label>URL：</label><input :name="'link' + index" v-model="slide.link" type="text">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center w-100"><button class="sendbtn btn" @click="formSubmit">儲存</button></div>
                    </div>
                </div>
            </form>
        </div> -->

        <!-- 更換資訊 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeInfoBox">
            跳出視窗
        </button>
        <div class="modal main-modal fade"  id="changeInfoBox" tabindex="-1" role="dialog" aria-labelledby="changeInfoBoxLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content changeInfo">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                <div class="modal-body">
                    <form>
                        <div class="infobox"><label>LINE：</label><input type="text" v-model="line"></div>
                        <div class="infobox"><label>TikTok：</label><input type="text" v-model="tiktok"></div>
                        <div class="infobox"><label>WeChat：</label><input type="text" v-model="wechat"></div>
                        <div class="infobox"><label>YouTube：</label><input type="text" v-model="youtube"></div>
                        <div class="infobox"><label>IG：</label><input type="text" v-model="ig"></div>
                        <div class="infobox"><label>FB：</label><input type="text" v-model="fb"></div>
                        <div class="infobox"><label>E-mail：</label><input type="text" v-model="email"></div>
                        <div class="infobox"><label>Phone：</label><input type="text" v-model="phone"></div>
                    </form>            
                </div>
                <div class="modal-footer">
                    <button class="sendbtn btn" @click="ajaxSubmit">儲存</button>
                </div>
              </div>
            </div>
        </div>


        <!-- 限時採購 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeLimitBox">
            跳出視窗
        </button>
        <div class="modal main-modal fade" id="changeLimitBox" tabindex="-1" role="dialog" aria-labelledby="changeLimitBoxLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content changeInfo" id="change-limit-box">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                  <div class="modal-header">
                    <h5 class="modal-title" id="changeLimitBoxLabel">廣告時程表</h5>
                  </div>

                <div class="modal-body">
                   
                    <div class="row">
                        <div class="col-md-6">
                            <label>開始日期</label>
                            <input class="form-control mb-1" type="date" v-model="start">
                        </div>
                        <div class="col-md-6">
                            <label>結束日期</label>
                            <input class="form-control mb-1" type="date" v-model="end">
                            <button @click="noTimeLimit" class="btn whitebtn">關閉</button>
                            <button @click="hasTimeLimit" class="btn clearbtn">設定結束日期 ( 設為今天 ) </button>
                        </div>
                        <div class="col-12 mt-3 d-flex justify-content-center"><button class="sendbtn btn mt-1" @click="timeAjaxSubmit">儲存</button></div>
                    </div>
                   
                    <form class="mt-3 border-top pt-3">
                        @csrf
                        <div class="index-add">
                            <div class="item"><label>1. 商品ID：</label><input type="text" v-model="product_id1"></div>
                            <div class="item"><label>2. 商品ID：</label><input type="text" v-model="product_id2"></div>
                            <div class="item"><label>3. 商品ID：</label><input type="text" v-model="product_id3"></div>
                            <div class="item"><label>4. 商品ID：</label><input type="text" v-model="product_id4"></div>
                            <div class="item"><label>5. 商品ID：</label><input type="text" v-model="product_id5"></div>
                            <div class="item"><label>6. 商品ID：</label><input type="text" v-model="product_id6"></div>
                            <div class="item"><label>7. 商品ID：</label><input type="text" v-model="product_id7"></div>
                            <div class="item"><label>8. 商品ID：</label><input type="text" v-model="product_id8"></div>
                            <div class="item"><label>9. 商品ID：</label><input type="text" v-model="product_id9"></div>
                            <div class="item"><label>10. 商品ID：</label><input type="text" v-model="product_id10"></div>
                        </div> 
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="sendbtn btn mt-1" @click="productAjaxSubmit">儲存</button>
                </div>
              </div>
            </div>
          </div>


        <!-- 其他商品 人氣+店長+即期 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeProductBox">
            跳出視窗
        </button>
        <div class="modal main-modal fade" id="changeProductBox" tabindex="-1" role="dialog" aria-labelledby="changeProductBoxLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-body changebody">
                    <form style="margin:0">
                        @csrf
                        <h4 class="text-danger remark">* 若無商品請填入0</h4>
                        <div class="index-add">
                            <div class="item"><label>1. 商品ID：</label><input type="text" v-model="product_id1"></div>
                            <div class="item"><label>2. 商品ID：</label><input type="text" v-model="product_id2"></div>
                            <div class="item"><label>3. 商品ID：</label><input type="text" v-model="product_id3"></div>
                            <div class="item"><label>4. 商品ID：</label><input type="text" v-model="product_id4"></div>
                            <div class="item"><label>5. 商品ID：</label><input type="text" v-model="product_id5"></div>
                            <div class="item"><label>6. 商品ID：</label><input type="text" v-model="product_id6"></div>
                            <div class="item"><label>7. 商品ID：</label><input type="text" v-model="product_id7"></div>
                            <div class="item"><label>8. 商品ID：</label><input type="text" v-model="product_id8"></div>
                            <div class="item"><label>9. 商品ID：</label><input type="text" v-model="product_id9"></div>
                            <div class="item"><label>10. 商品ID：</label><input type="text" v-model="product_id10"></div>
                        </div> 
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="sendbtn btn" @click="ajaxSubmit">儲存</button>
                </div>
              </div>
            </div>
        </div>



        <!-- 推薦圖文 -->
        <!-- <div id="changeImgtextBox">        
            <div id="change-imgtext-box" v-if="visibility"> 
                <form name="changeImgtextForm" :action="action" method="post" target="changeImgtextFormIframe" enctype="multipart/form-data">            
                    @csrf
                    <span class="bi bi-x-circle-fill" @click="setHidden"></span>
                    <div class="image-box">
                        <input type='file' ref="img" class="upl" name="image" @change="previewImg" accept="image/*">
                        <img :src="src" class="preview preImg" />
                    </div>
                    <p>建議大小：600*340</p>
                    <div style="position:relative; left:15%; margin-bottom:5%">
                        <label>URL：</label>
                        <input v-model="link" name="link" style="border:none; border-bottom:1px solid #000000; width:60%;" type="text">
                    </div>
                    <input v-model="location" name="location" type="hidden">

                
                    <div style="position:relative; left:15%; width:70%">
                        <table style="width:100%;">
                            <tr>
                                <td><input style="width:100%; border:2px solid #000000;"
                                            type="text" name="title" v-model="title"></td>
                            </tr>
                            <tr>
                                <td><textarea style="width:100%; height:170px; border:2px solid #000000; border-top:none; resize: none;"
                                                type="text" v-model="content"></textarea></td>    
                                <input name="content" type="hidden" v-model="contentNl2br">
                            </tr>
                        </table>
                    </div>
                    <button class="send-btn" @click="formSubmit">儲存</button>
                </form
            </div>
        </div> -->

        <!-- 文字編輯器 -->
        <button type="button" class="btn d-none" data-toggle="modal" data-target="#changeTextBox">
            跳出視窗
        </button>
        <div class="modal main-modal fade" id="changeTextBox"  role="dialog" aria-labelledby="changeTextBoxLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                  </button>
                <div class="modal-body changebody">
                    <p>標題：<input type="text" v-model="title"></p>
                    
                    <input type="hidden" v-model="content">
                    <input type="hidden" id="editor">
                    
                </div>
                <div class="modal-footer">
                    <button class="sendbtn btn" @click="ajaxSubmit">儲存</button>
                </div>
              </div>
            </div>
        </div>


  

        <div class="content">
            <table class="indexview" style="min-width: 1400px;">
                <tr></tr>
                <tr>
                    <!-- LOGO -->
                    <td style="width:80px;">LOGO</td>
                    <td style="width:50px;"></td>
                    <td style="width:900px;;" class="text-left">
                        <span id="firstRowLeft">
                            <div class="change-img" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" style="width:250px; cursor:pointer;">
                                <img :src="src" class="preImg" alt="600*120"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>

                            <div style="border: none;">
                                顯示商品總選單：
                                <label class="switch">
                                    <input type="checkbox" v-model="product_nav_total" @change="onlineToggle('product_nav_total')">
                                    <span class="slider round"></span>
                                </label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                顯示其他選單：
                                <label class="switch">
                                    <input type="checkbox" v-model="nav_other" @change="onlineToggle('nav_other')">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </span>
    					
                        <!--<a id="firstRowRight">
                             <div class="change-img" @click="openBox" style="width:80px; height:40px; cursor:pointer;">
                                <img :src="src" class="preImg" alt="240*140"/>
                                <span class="bi bi-pencil-square"></span>
                            </div> 
                        </a>-->
                    </td>

                    <!-- 跳出廣告  -->
                    <td  rowspan="19" style="min-width:200px;">
                        <a id="RightADV" style="position: absolute; top: 10%;left:0;">
                            <label class="switch" style="position: absolute; z-index: 5">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                            <div class="change-img" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" style="min-width:180px;  cursor:pointer;">
                                <img :src="src" class="preImg" alt="480*640"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </a>
                    </td>
                    <!-- 聯絡方式  -->
                    <td style="width:70px" rowspan="19" id="fixADV">
                        <div class="change-info" @click="openBox" data-toggle="modal" data-target="#changeInfoBox"
                            style="border:none; position: absolute; cursor:pointer; top: 10%;">
                            <ul class="iconbox">
                                <li><img src="{{__PUBLIC__}}/image/icon_boxig.png"></li>
                                <li><img src="{{__PUBLIC__}}/image/icon_boxline.png"></li>
                                <li><img src="{{__PUBLIC__}}/image/icon_boxfb.png"></li>
                                <li><img src="{{__PUBLIC__}}/image/icon_boxmail.png"></li>
                                <li><img src="{{__PUBLIC__}}/image/icon_boxcall.png"></li>
                                <span class="bi bi-pencil-square"></span>
                            </ul>
                        </div>
                    </td>
                </tr>

                <!-- 輪播圖片 -->
                <tr id="slideShow">
                    <td>輪播圖片</td>
                    <td>
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>

                    <td>
                        <div v-if="online" style="border:none;padding:0; margin:0; display:unset">
                            <div class="change-img-group position-relative d-flex m-0" @click="openBox" data-toggle="modal" data-target="#changeGroupBox"  style="height:200px; overflow: hidden;  cursor:pointer">
                                <img :src="src" class="preImg" style="position: absolute;" alt="1260*350"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>
                            <div style="width: 98%; display: flex;">
                                <div v-for="(slide, index) in slideshow" 
                                     @mouseenter="switchSrc(index)">
                                    <img :src="slide.src" :class="['preview', slide.online==0 ? 'opacity_05' : '']"/>
                                </div>
                            </div>
                        </div>
                        <p v-else style="margin:10px">輪播圖片待開啟</p>
                    </td>
                </tr>

                <!-- 影片嵌入 -->
                <tr id="iframeADV">
                    <td>影片嵌入</td>
                    <td>
                        <div style="position: absolute;left: -5;padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td>
                        <div v-if="online" style="min-height: 25px; border:0px; display: block;">
                            嵌入程式碼：
                            <div style="width: 80%; border:2px solid; ">
                                <textarea v-model="iframe" placeholder="請貼上程式碼" @blur="save"
                                          style="resize: vertical;"></textarea>
                            </div>
                            <p class="text-danger remark">如欲自動播放需於src中加入 autoplay=1 mute=1 兩參數(需修改嵌入程式)</p>
                        </div>
                        <p v-else style="margin:10px">影片嵌入待開啟</p>
                    </td>
                </tr>

                @if(empty(config('control.close_function_current')['最新消息']))
                    <!-- 最新消息-->
                    <tr id="news_ADV">
                        <td>{{$data['frontend_menu']['news']['name']}}</td>
                        <td>
                            <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" style="border:none; padding:0; margin:0 auto" name="5">
                                <div class="change-product" style="width:500px; height:50px; cursor:pointer" onclick="location.href = '{{url('news/index')}}'">
                                    {{$data['frontend_menu']['news']['name']}}(有消息才顯示)
                                </div>
                            </div>
                            <p v-else style="margin:10px" name="5">{{$data['frontend_menu']['news']['name']}}待開啟</p>
                        </td>
                    </tr>
                @endif

                @if(config('control.control_index_edm')==1)
                    <!-- EDM -->
                    <tr id="edm">
                        <td>EDM</td>
                        <td>
                            <div style="position: absolute;left: -5;padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" style="height: 25px; border:0px; display: block;">
                                EDM ID：<input type="number"  v-model="edm_id" placeholder="請輸入EDM ID" @blur="saveId">
                            </div>
                            <p v-else style="margin:10px">EDM待開啟</p>
                        </td>
                    </tr>
                @endif

                <!-- 橫幅廣告(矮) -->
                <tr id="titleADV">
                    <td>橫幅廣告(矮)</td>
                    <td>
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>

                    <td>
                        <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox"  style="border:none; padding:0; margin:0" name="1">
                            <div class="change-img" style="width:500px;  cursor:pointer">
                                <img :src="src" class="preImg" alt="1410*282"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </div>
                        <p v-else style="margin:10px">橫幅廣告(矮)待開啟</p>
                    </td>
                </tr>

                <!-- 多廣告 -->
                <tr>
                    <td>多廣告</td>
                    <td id="threeADVCtrl">
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td id="threeADVCtrlHiddenHint">
                        <div v-if="hidden"
                             onclick="location.href='/admin/indexad/index'" class="change-product" 
                             style="width: 500px; height: 50px; cursor: pointer;">
                            多廣告
                        </div>
                        <p style="margin:10px" name="3"><span v-if="!hidden">多廣告待開啟</span></p>
                    </td>
                </tr>

                @if(config('control.control_time_limit_prod')==1)
                    <!-- 限時搶購 -->
                    <tr id="fifthADV">
                        <td>限時搶購</td>
                        <td></td>
                        <td>
                            <div class="change-limit" @click="openBox" data-toggle="modal" data-target="#changeLimitBox" style="width:500px; height:50px; cursor:pointer">
                                限時搶購
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </td>
                    </tr>
                @endif

                <!-- 橫幅廣告-1 -->
                <tr id="SixthADV">
                    <td>橫幅廣告-1</td>
                    <td>
                        <div style="position: absolute;
                                    
                                    padding: 0;
                                    border: none;top:3px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" style="border:none; padding:0; margin:0" name="1">
                            <div class="change-img" style="width:500px;  cursor:pointer">
                                <img :src="src" class="preImg" alt="1410*470"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </div>
                        <p v-else style="margin:10px">橫幅廣告-1待開啟</p>
                    </td>
                </tr>

                @if(empty(config('control.close_function_current')['標籤設定']))
                    <!-- 人氣商品 -->
                    <tr id="seventhADV">
                        <td>{{$data['tag'][0]['name']}}</td>
                        <td>
                            <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" @click="openBox" data-toggle="modal" data-target="#changeProductBox" style="border:none; padding:0; margin:0" name="5">
                                <div class="change-product" style="width:500px; height:50px; cursor:pointer">
                                    {{$data['tag'][0]['name']}}
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </div>
                            <p v-else style="margin:10px" name="5">{{$data['tag'][0]['name']}}待開啟</p>
                        </td>
                    </tr>
                @endif

                <!-- 圖文廣告 -->
                <tr>
                    <td>圖文廣告</td>
                    <td id="eighthADVCtrl">
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td>
                        <div style="border:none; border: none; display: flex; width: 100%; justify-content: center;" name="6">
                            <a id="eighthADVLeft">
                                <div class="change-img" v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" style="width:240px; height:200px; cursor:pointer;">
                                    <img :src="src" class="preImg" alt="690*485"/>
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </a>
                            <span id="eighthADVText" style="display: flex; flex-direction: column;">
                                <div v-if="online" style="width:240px; height:30px;">
                                    <input type="text" style="width:90%; border:none" v-model="title">
                                    <span class="bi bi-save-fill" @click="ajaxTitle"></span>
                                </div>
                                <div v-if="online" style="width:240px; height:80px;">
                                    <textarea type="text" v-model="content"></textarea>
                                    <span class="bi bi-save-fill" @click="ajaxContent"></span>
                                </div>
                                <span style="display: flex;">
                                    <div v-if="online" style="width:110px; height:90px;">
                                        ID：<input type="text" style="width:80%;" v-model="id1">
                                        <span class="bi bi-save-fill" @click="ajaxId1"></span>
                                    </div>
                                    <div v-if="online" style="width:110px; height:90px;">
                                        ID：<input type="text" style="width:80%;" v-model="id2">
                                        <span class="bi bi-save-fill" @click="ajaxId2"></span>
                                    </div>
                                </span>
                            </span>
                        </div>
                        <p id="eighthADVCtrlHiddenHint" style="margin:10px" name="3"><span v-if="!hidden">圖文廣告待開啟</span></p>
                    </td>
                </tr>

                @if(empty(config('control.close_function_current')['標籤設定']))
                    <!-- 第九列 店長推薦-->
                    <tr id="ninthADV">
                        <td>{{$data['tag'][1]['name']}}</td>
                        <td>
                            <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" @click="openBox" data-toggle="modal" data-target="#changeProductBox" style="border:none; padding:0; margin:0" name="5">
                                <div class="change-product" style="width:500px; height:50px; cursor:pointer">
                                    {{$data['tag'][1]['name']}}
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </div>
                            <p v-else style="margin:10px" name="5">{{$data['tag'][1]['name']}}待開啟</p>
                        </td>
                    </tr>
                @endif

                @if(empty(config('control.close_function_current')['標籤設定']) && config('control.control_sepc_price')==1)
                    <!-- 特價商品(不限數量全部顯示)-->
        			<tr id="spe_ADV">
                        <td>{{$data['tag'][3]['name']}}</td>
                        <td>
                            <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" style="border:none; padding:0; margin:0" name="5">
                                <div class="change-product" style="width:500px; height:50px; cursor:pointer" onclick="location.href = '{{url('index/spePriceProduct')}}'">
                                    {{$data['tag'][3]['name']}}
                                    <!--span class="bi bi-pencil-square"></span-->
                                </div>
                            </div>
                            <p v-else style="margin:10px" name="5">{{$data['tag'][3]['name']}}待開啟</p>
                        </td>
                    </tr>
                @endif

                <!-- 圖文廣告B
                <tr>
                    <td>圖文廣告B</td>
                    <td id="tenthADVCtrl">
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div style="border:none; padding:0; margin:0; display:table-row" name="8">
                            <a id="tenthADVText" style="color:black;">
                                <div v-if="online" style="width:240px; height:30px; justify-content:left;">
                                    <input type="text" style="width:80%; border:none" v-model="title">
                                    <span class="bi bi-save-fill" @click="ajaxTitle"></span>
                                </div>
                                <div v-if="online" style="width:240px; height:80px;
                                            position:absolute;
                                            left:5px; top:35px; margin-top:8px;
                                            justify-content:left; align-items:left; text-align:left">
                                    <textarea type="text" v-model="content"></textarea>
                                    <span class="bi bi-save-fill" @click="ajaxContent"></span>
                                </div>
                                <div v-if="online" style="width:240px; height:40px;
                                            position:absolute;
                                            left:5px; top:125px;">
                                    URL：<input type="text" style="width:80%;" v-model="url">

                                    <span class="bi bi-save-fill" @click="ajaxUrl"></span>
                                </div>
                            </a>
                            <a id="tenthADVRight">
                                <div class="change-img" v-if="online" @click="openBox" style="width:240px; height:160px; float:right; cursor:pointer">
                                    <img :src="src" class="preImg" alt="700*475"/>
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </a>
                        </div>
                        <p id="tenthADVCtrlHiddenHint" style="margin:10px" name="8"><span v-if="!hidden">圖文廣告B待開啟</span></p>

                    </td>
                </tr>-->

                <!-- 橫幅廣告-2 -->
                <tr id="new_tenthADV">
                    <td>橫幅廣告-2</td>
                    <td>
                        <div style="position: absolute;
                                    
                                    padding: 0;
                                    border: none;top: 3px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" style="border:none; padding:0; margin:0" name="1">
                            <div class="change-img" style="width:500px; height:161px; cursor:pointer">
                                <img :src="src" class="preImg" alt="1920*635"/>
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </div>
                        <p v-else style="margin:10px">橫幅廣告-2待開啟</p>
                    </td>
                </tr>

                @if(empty(config('control.close_function_current')['標籤設定']))
                    <!-- 即期良品 -->
                    <tr id="eleventhADV">
                        <td>{{$data['tag'][2]['name']}}</td>
                        <td>
                            <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                    <label class="switch">
                                        <input type="checkbox" v-model="online" @change="onlineToggle">
                                        <span class="slider round"></span>
                                    </label>
                            </div>
                        </td>
                        <td>
                            <div v-if="online" @click="openBox" data-toggle="modal" data-target="#changeProductBox" style="border:none; padding:0; margin:0" name="5">
                                <div class="change-product" style="width:500px; height:50px; cursor:pointer">
                                    {{$data['tag'][2]['name']}}
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </div>
                            <p v-else style="margin:10px" name="5">{{$data['tag'][2]['name']}}待開啟</p>
                        </td>
                    </tr>
                @endif

                <!-- 三幅廣告 -->
                <tr>
                    <td>三幅廣告</td>
                    <td id="new_thirteenthADVCtrl">
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td>
                        <div style="border:none; padding:0; margin:0" name="3">
                            <a id="new_thirteenthADVLeft">
                                <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" class="change-img" style="width:164px; height:164px; margin:10px 0px; margin-left:10px; cursor:pointer">
                                    <img :src="src" class="preImg" alt="470*470"/>
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </a>
                            <a id="new_thirteenthADVCenter">
                                <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" class="change-img" style="width:164px; height:164px; margin:10px 0px; margin-left:10px; cursor:pointer">
                                    <img :src="src" class="preImg" alt="470*470"/>
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </a>
                            <a id="new_thirteenthADVRight">
                                <div v-if="online" @click="openBox"  data-toggle="modal" data-target="#changeImageBox" class="change-img" style="width:164px; height:164px; margin:10px 0px; margin-left:10px; cursor:pointer">
                                    <img :src="src" class="preImg" alt="470*470"/>
                                    <span class="bi bi-pencil-square"></span>
                                </div>
                            </a>
                        </div>
                        <p id="new_thirteenthADVCtrlHiddenHint" style="margin:10px" name="3"><span v-if="!hidden">三幅廣告待開啟</span></p>
                    </td>
                </tr>

                <!-- 分館廣告 -->
                <tr id="twelfthADVVM">
                    <td>分館廣告</td>
                    <td>
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td style="width: 500px">
                        <span v-if="online" :list="list" @end="endDrag">
                            <div style="width:97px; height: 56px;" v-for="element in list">
                                <p style="position: absolute; top: 5;">
                                    <a :href="'/admin/product/index?id=' + element.id" target="_blank" style="color: #007bff;" v-text="element.title"></a>
                                </p>
                                <select style="position: absolute; top: 30px;
                                            display: block;
                                            width: 97px;
                                            border: 2px solid;
                                            left:-2px;" v-model="element.online" @change="onChange">
                                    <option value="1">開啟</option>
                                    <option value="0">關閉</option>
                                </select>
                            </div>
                        </span>
                        <p v-else style="margin:10px" name="10">分館廣告待開啟</p>
                    </td>
                </tr>

                <!-- 推薦圖文 
                <tr>
                    <td>推薦圖文</td>
                    <td id="thirteenthADVCtrl">
                        <div style="position: absolute; padding: 0; border: none;top: 0px;left: -3px;">
                                <label class="switch">
                                    <input type="checkbox" v-model="online" @change="onlineToggle">
                                    <span class="slider round"></span>
                                </label>
                        </div>
                    </td>
                    <td>
                        <div style="border:none; padding:0; margin:0" name="11">
                            <a id="thirteenthRowLeft" style="color: black;" @click="openBox">
                                <div v-if="online" class="change-imgtext" style="width:248px; height: 260px; position: relative; margin:0px; margin-left:10px; margin-top:10px; cursor:pointer;">
                                        <img :src="src" style="position:absolute; top:0%; left:0%; max-width:100%; max-height:50%;" alt="600*340"/>
                                    <span class="bi bi-pencil-square"></span>
                                    <div style="position: absolute; top: 130px; margin:0;
                                                display: block;
                                                width: 248px; height:128px;
                                                border: 2px solid;">
                                        <p style="font-size:13px; text-align:left">
                                            <font style="font-size:14px" v-text="title"></font><br>
                                            <textarea readonly style="height:100px" v-model="content"></textarea>
                                        </p>
                                    </div>
                                </div>
                            </a>
                            <a id="thirteenthRowRight" style="color: black;" @click="openBox">
                                <div v-if="online" class="change-imgtext" style="width:248px; height: 260px; position: relative; margin:0px; margin-right:10px; margin-top:10px; cursor:pointer; margin-left: -2px;">
                                    <img :src="src" style="position:absolute; top:0%; left:0%; max-width:100%; max-height:50%;" alt="600*340"/>
                                    <span class="bi bi-pencil-square"></span>
                                    <div style="position: absolute; top: 130px; margin:0;
                                                display: block;
                                                width: 248px; height:128px;
                                                border: 2px solid;">
                                        <p style="font-size:13px; text-align:left">
                                            <font style="font-size:14px" v-text="title"></font><br>
                                            <textarea readonly style="height:100px" v-model="content"></textarea>
                                        </p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <p id="thirteenthADVCtrlHiddenHint" style="margin:10px" name="8"><span v-if="!hidden">推薦圖文待開啟</span></p>
                    </td>
                </tr>-->

                <!-- 頁尾選單 -->
                <tr id="footer_nav">
                    <td>頁尾選單</td>
                    <td>
                        <div style="position: absolute; padding: 0; border: none; top: 0px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="nav_other_footer" @change="onlineToggle('nav_other_footer')">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>
                    <td>
                        <p v-if="nav_other_footer" style="margin:10px" name="12">頁尾選單已開啟</p>
                        <p v-else style="margin:10px" name="12">頁尾選單待開啟</p>
                    </td>
                </tr>

                <!-- 頁尾區域文字 -->
                <tr id="fourteenthADV">
                    <td>頁尾區域文字</td>
                    <td>
                        <div style="position: absolute; padding: 0; border: none; top: 0px;left: -3px;">
                            <label class="switch">
                                <input type="checkbox" v-model="online" @change="onlineToggle">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </td>
                    <td>
                        <div v-if="online" style="border:none; padding:0; margin:0" name="12">
                            <div @click="openBox(1)" data-toggle="modal" data-target="#changeTextBox" class="change-text" style="width:125px; height: 50px; position: relative; margin:0px; margin-left:10px; margin-top:10px; cursor:pointer">
                                A區域文字編輯
                                <span class="bi bi-pencil-square"></span>
                            </div>
                            <div @click="openBox(2)" data-toggle="modal" data-target="#changeTextBox" class="change-text" style="width:125px; height: 50px; position: relative; margin:0px; margin-top:10px; margin-left:-3px; cursor:pointer">
                                B區域文字編輯
                                <span class="bi bi-pencil-square"></span>
                            </div>
                            <div @click="openBox(3)" data-toggle="modal" data-target="#changeTextBox" class="change-text" style="width:125px; height: 50px; position: relative; margin:0px; margin-top:10px; margin-left:-3px; cursor:pointer">
                                C區域文字編輯
                                <span class="bi bi-pencil-square"></span>
                            </div>
                            <div @click="openBox(4)" data-toggle="modal" data-target="#changeTextBox" class="change-text" style="width:125px; height: 50px; position: relative; margin:0px; margin-right:10px; margin-left:-3px; margin-top:10px; cursor:pointer">
                                D區域文字編輯
                                <span class="bi bi-pencil-square"></span>
                            </div>
                        </div>
                        <p v-else style="margin:10px" name="12">頁尾區域文字待開啟</p>
                    </td>
                </tr>
            </table>
        </div>
    </div>
@endsection

@section('ownJS')

    <!--
    <script src="http://ajax.aspnetcdn.com/ajax/knockout/knockout-3.0.0.js "></script>
    <script src="http://cdn.kendostatic.com/2013.3.1119/js/kendo.core.min.js"></script>
    -->

    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function(K) {
                editor = K.create('#editor', {
                        afterBlur: function(){this.sync();},
                        langType : 'zh_TW',
                        items:['source', '|', 'image', 'link', 'unlink'],
                        width:'100%',
                        height:'300px',
                        resizeType:0
                });
        });
    </script>

    <script>
        /*init prototype method that will use*/
        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Index/blockCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setSocialLink = function (linkData) {
            $.ajax({
                url: "{{url('Index/setSocialLink')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: linkData,
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                        changeInfoBoxVM.updateCallerData();
                        maskVM.setHidden();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setText = function (id, text) {
            $.ajax({
                url: "{{url('Index/setText')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: id,
                    text: text
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setLink = function (id, link) {
            $.ajax({
                url: "{{url('Index/setLink')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: id,
                    link: link
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setProductOnline = function (id, online) {
            $.ajax({
                url: "{{url('Index/setProductOnline')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: id,
                    online: online
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setProductOrder = function (idArray) {
            $.ajax({
                url: "{{url('Index/setProductOrder')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    idArray: JSON.stringify(idArray)
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setTimeRange = function (start, end) {
            $.ajax({
                url: "{{url('Index/setTimeRange')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    start: start,
                    end: end
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                        changeLimitBoxVM.updateCallerTime();
                        maskVM.setHidden();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setProduct = function (productData) {
            $.ajax({
                url: "{{url('Index/setProduct')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: productData,
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                        if(changeProductBoxVM.caller){
                            changeProductBoxVM.updateCallerData();
                        }
                        if(changeLimitBoxVM.caller){
                            changeLimitBoxVM.updateCallerData();
                        }
                        maskVM.setHidden();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.setContent = function (id, content, column="data3") {
            $.ajax({
                url: "{{url('Index/setText')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: {
                    id: id,
                    text: content,
                    column: column,
                },
                success: function(response) {
                    if(response.status){
                        Vue.toasted.show('更改成功', vt_success_obj);
                        changeTextBoxVM.setHidden();
                        changeTextBoxVM.updateCallerData();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.formatDate = function (date) {
            var d = date,
                month = '' + (d.getMonth() + 1),
                day = '' + d.getDate(),
                year = d.getFullYear();

            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;
            return [year, month, day].join('-');
        }

        /*mask and uploadBox*/
        var mask = {
            visibility:　false
        }
        // var maskVM = new Vue({
        //     el: '#mask', 
        //     data: mask,
        //     methods: {
        //         setHidden: function () {
        //             this.visibility = false;
        //             changeImageBox.visibility = false;
        //             changeGroupBox.visibility = false;
        //             changeInfoBox.visibility = false;
        //             changeProductBox.visibility = false;
        //             changeImgtextBox.visibility = false;
        //             changeTextBox.visibility = false;
        //             changeLimitBox.visibility = false;
        //         }
        //     }
        // });

        var changeImageBox = {
            visibility:　false,
            width: 0,
            height: 0,
            link: '',
            src: '',
            id: 0,
            action: "{{url('Index/setPicWithLink')}}",
            caller: null
        }
        var changeImageBoxVM = new Vue({
            el: '#changeImageBox', 
            data: changeImageBox,
            methods: {
                setHidden: function () {
                    maskVM.setHidden();
                },
                formSubmit: function () {
                    document.changeImageBoxForm.submit();
                },
                previewImg: function () {
                    console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        changeImageBox.src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                updateCallerData: function () {
                    changeImageBox.caller.src = this.src;
                    changeImageBox.caller.link = this.link;
                    $('#changeImageBox').modal('hide');
                }
            }
        });

        $('#changeImageFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                Vue.toasted.show('上傳成功', vt_success_obj);
                changeImageBoxVM.updateCallerData();
                maskVM.setHidden();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
        });

        $('#changeGroupFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                Vue.toasted.show('上傳成功', vt_success_obj);
                changeGroupBoxVM.updateCallerData();
                maskVM.setHidden();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
        });


        // var changeImgtextBox = {
        //     visibility:　false,
        //     title: "", src: "",
        //     content: "", caller: null,
        //     location: "", link: "",
        //     action: "{{url('Index/setthirteenth')}}"
        // }
        // var changeImgtextBoxVM = new Vue({
        //     el: '#changeImgtextBox', 
        //     data: changeImgtextBox,
        //     computed: {
        //         contentNl2br: function () {
        //             return this.content.replace(/\n/g, '<br>');
        //         }
        //     },
        //     methods: {
        //         setHidden: function () {
        //             maskVM.setHidden();
        //         },
        //         formSubmit: function () {
        //             document.changeImgtextForm.submit();
        //         },
        //         previewImg: function () {
        //             console.log(this.$refs.img.files);
        //             var reader = new FileReader();
        //             reader.onload = function (e) {
        //                 changeImgtextBox.src = e.target.result;
        //             }
        //             reader.readAsDataURL(this.$refs.img.files[0]);
        //         },
        //         updateCallerData: function () {
        //             this.caller.src = this.src;
        //             this.caller.link = this.link;
        //             this.caller.title = this.title;
        //             this.caller.content = this.content;
        //         }
        //     }
        // });

        $('#changeImgtextFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                Vue.toasted.show('上傳成功', vt_success_obj);
                changeImgtextBoxVM.updateCallerData();
                maskVM.setHidden();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
        });


        var changeInfoBox = {
            visibility:　false,
            line: "",
            tiktok: "",
            wechat: "",
            youtube: "",
            ig: "",
            fb: "",
            email: "",
            phone: "",
            caller: null
        }
        var changeInfoBoxVM = new Vue({
            el: '#changeInfoBox', 
            data: changeInfoBox,
            methods: {
                setHidden: function () {
                    maskVM.setHidden();
                },
                ajaxSubmit: function () {
                    newData = {
                        line: this.line,
                        tiktok: this.tiktok,
                        wechat: this.wechat,
                        youtube: this.youtube,
                        ig: this.ig,
                        fb: this.fb,
                        email: this.email,
                        phone: this.phone,
                    }
                    this.setSocialLink(newData);
                },
                updateCallerData: function () {
                    changeInfoBox.caller.line = this.line;
                    changeInfoBox.caller.tiktok = this.tiktok;
                    changeInfoBox.caller.wechat = this.wechat;
                    changeInfoBox.caller.youtube = this.youtube;
                    changeInfoBox.caller.ig = this.ig;
                    changeInfoBox.caller.fb = this.fb;
                    changeInfoBox.caller.email = this.email;
                    changeInfoBox.caller.phone = this.phone;
                    $('#changeInfoBox').modal('hide');
                }
            }
        });

        
        var changeProductBox = {
            visibility:　false,
            product_id1: "", product_id2: "",
            product_id3: "", product_id4: "",
            product_id5: "", product_id6: "",
            product_id7: "", product_id8: "",
            product_id9: "", product_id10: "",
            tableName:"" ,caller: null
        }
        var changeProductBoxVM = new Vue({
            el: '#changeProductBox', 
            data: changeProductBox,
            methods: {
                setHidden: function () {
                    maskVM.setHidden();
                },
                ajaxSubmit: function () {
                    newData = {
                        tableName: this.tableName, 
                        product_id1: this.product_id1, product_id2: this.product_id2, 
                        product_id3: this.product_id3, product_id4: this.product_id4, 
                        product_id5: this.product_id5, product_id6: this.product_id6, 
                        product_id7: this.product_id7, product_id8: this.product_id8, 
                        product_id9: this.product_id9, product_id10: this.product_id10,
                    }
                    this.setProduct(newData);
                },
                updateCallerData: function () {
                    this.caller.product_id1 = this.product_id1; this.caller.product_id6 = this.product_id6;
                    this.caller.product_id2 = this.product_id2; this.caller.product_id7 = this.product_id7;
                    this.caller.product_id3 = this.product_id3; this.caller.product_id8 = this.product_id8;
                    this.caller.product_id4 = this.product_id4; this.caller.product_id9 = this.product_id9;
                    this.caller.product_id5 = this.product_id5; this.caller.product_id10 = this.product_id10;
                    $('#changeProductBox').modal('hide');
                }
            }
        });


    /* Row */
    /* logo start */
        var firstRowLeft = {
            link: "{{$data['index_excel'][0]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][0]['data1']}}",
            product_nav_total: +"{{$data['index_online']['product_nav_total']}}",
            nav_other: +"{{$data['index_online']['nav_other']}}",
        }
        var firstRowLeftVM = new Vue({
            el: '#firstRowLeft',
            data: firstRowLeft,
            methods: {
                openBox: function () {
                    changeImageBox.id = 1;
                    changeImageBox.width = 268;
                    changeImageBox.height = 67;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    // mask.visibility = true;
                },
                onlineToggle: function (target) {
                    //console.log(this.online);
                    blockData = {
                        id: 1,
                    }
                    blockData[target] = this[target] ? 1 : 0;
                    this.blockCtrl(blockData);
                },
            }
        });
        // var firstRowRight = {
        //     link: "{{$data['index_excel'][1]['data2']}}",
        //     src: "{{__UPLOAD__}}{{$data['index_excel'][1]['data1']}}"
        // }

        // var firstRowRightVM = new Vue({
        //     el: '#firstRowRight',
        //     data: firstRowRight,
        //     methods: {
        //         openBox: function () {
        //             changeImageBox.id = 2;
        //             changeImageBox.width = 240;
        //             changeImageBox.height = 140;
        //             changeImageBox.link = this.link;
        //             changeImageBox.src = this.src;
        //             changeImageBox.visibility = true;
        //             changeImageBox.caller = this;
        //             mask.visibility = true;
        //         }
        //     }
        // });
    /* logo end */


    /* 跳出廣告 start */
        var RightADV = {
            link: "{{$data['index_excel'][6]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][6]['data1']}}",
            online: +"{{$data['index_online']['block13']}}",
        }

        var RightADVVM = new Vue({
            el: '#RightADV',
            data: RightADV,
            methods: {
                openBox: function () {
                    changeImageBox.id = 7;
                    changeImageBox.width = 480;
                    changeImageBox.height = 640;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block13: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /* 跳出廣告 end */

    /* 聯絡方式 start */
        var fixADV = {
            line: "{{$data['index_excel'][2]['data2']}}",
            tiktok: "{{$data['index_excel'][39]['data2']}}",
            wechat: "{{$data['index_excel'][40]['data2']}}",
            youtube: "{{$data['index_excel'][41]['data2']}}",
            ig: "{{$data['index_excel'][38]['data2']}}",
            fb: "{{$data['index_excel'][3]['data2']}}",
            email: "{{$data['index_excel'][4]['data2']}}",
            phone: "{{$data['index_excel'][5]['data2']}}",
        }
        var fixADVVM = new Vue({
            el: '#fixADV',
            data: fixADV,
            methods: {
                openBox: function () {
                    changeInfoBox.line = this.line;
                    changeInfoBox.tiktok = this.tiktok;
                    changeInfoBox.wechat = this.wechat;
                    changeInfoBox.youtube = this.youtube;
                    changeInfoBox.ig = this.ig;
                    changeInfoBox.fb = this.fb;
                    changeInfoBox.email = this.email;
                    changeInfoBox.phone = this.phone;
                    changeInfoBox.visibility = true;
                    changeInfoBox.caller = this;
                    mask.visibility = true;
                }
            }
        });
    /* 聯絡方式 end */


    /* 橫幅廣告(矮) start */
        var titleADV = {
            link: "{{$data['index_excel'][7]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][7]['data1']}}",
            online: +"{{$data['index_online']['block1']}}"
        }
        var titleADVVM = new Vue({
            el: '#titleADV',
            data: titleADV,
            methods: {
                openBox: function () {
                    changeImageBox.id = 8;
                    changeImageBox.width = 1410;
                    changeImageBox.height = 282;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block1: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /* 橫幅廣告(矮) end */

    /* 輪播圖片 start*/
         // 跳出視窗 
        var changeGroupBox = {
            visibility:　false,
            slideshow : [
                {title: "", link: "", src: "", online: 1},
                {title: "", link: "", src: "", online: 1},
                {title: "", link: "", src: "", online: 1},
                {title: "", link: "", src: "", online: 1},
                {title: "", link: "", src: "", online: 1},
            ],
            src: "", caller: null,
            action: "{{url('Index/setSlideShow')}}"
        }
        var changeGroupBoxVM = new Vue({
            el: '#changeGroupBox', 
            data: changeGroupBox,
            methods: {
                setHidden: function () {
                    maskVM.setHidden();
                },
                formSubmit: function () {
                    document.changeGroupBoxForm.submit();
                },
                previewImg: function (number) {
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        changeGroupBox.src = e.target.result;
                        changeGroupBox.slideshow[number].src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs['img' + number][0].files[0]);
                },
                updateCallerData: function () {
                    changeGroupBox.caller.src = this.src;
                    changeGroupBox.caller.slideshow = this.slideshow;
                    $('#changeGroupBox').modal('hide');
                },
                switchSrc: function (number) {
                    this.src = this.slideshow[number].src;
                }
            }
        });

        // 表格
        var slideShow = {
            slideshow: [
                { title: "{{$data['slideshow'][0]['title']}}", link: "{{$data['slideshow'][0]['link']}}", src: "{{__UPLOAD__}}{{$data['slideshow'][0]['pic']}}", online: {{$data['slideshow'][0]['online']}} },
                { title: "{{$data['slideshow'][1]['title']}}", link: "{{$data['slideshow'][1]['link']}}", src: "{{__UPLOAD__}}{{$data['slideshow'][1]['pic']}}", online: {{$data['slideshow'][1]['online']}} }, 
                { title: "{{$data['slideshow'][2]['title']}}", link: "{{$data['slideshow'][2]['link']}}", src: "{{__UPLOAD__}}{{$data['slideshow'][2]['pic']}}", online: {{$data['slideshow'][2]['online']}} },
                { title: "{{$data['slideshow'][3]['title']}}", link: "{{$data['slideshow'][3]['link']}}", src: "{{__UPLOAD__}}{{$data['slideshow'][3]['pic']}}", online: {{$data['slideshow'][3]['online']}} },
                { title: "{{$data['slideshow'][4]['title']}}", link: "{{$data['slideshow'][4]['link']}}", src: "{{__UPLOAD__}}{{$data['slideshow'][4]['pic']}}", online: {{$data['slideshow'][4]['online']}} },
            ],
            src: "{{__UPLOAD__}}{{$data['slideshow'][0]['pic']}}",
            online: +"{{$data['index_online']['block2']}}",
        }
        var slideShowVM = new Vue({
            el: '#slideShow',
            data: slideShow,
            methods: {
                openBox: function () {
                    changeGroupBox.slideshow = this.slideshow;
                    changeGroupBox.src = this.src;
                    changeGroupBox.visibility = true;
                    changeGroupBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    //console.log(this.online);
                    blockData = {
                        id: 1,
                        block2: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                },
                switchSrc: function (number) {
                    this.src = this.slideshow[number].src;
                },
            }
        });
    /* 輪播圖片 end*/

    /*影片嵌入 start*/
        var iframe_content = function(){
            /*{!! $data['index_excel'][37]['data3'] !!}*/
        }.toString().replace(/[\\]/g,"") + '\n';
        iframe_content = iframe_content.split('*');
        delete iframe_content[0];
        var lastnum = iframe_content.length -1;
        delete iframe_content[lastnum];
        var iframeADV = {
            online: +"{{$data['index_online']['block_iframe']}}",
            iframe: iframe_content.join(''),
            index_excel_id: 38,
        }
        var iframeADVVM = new Vue({
            el: '#iframeADV',
            data: iframeADV,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block_iframe: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                },
                save: function () {
                    // console.log(this.index_excel_id, this.iframe);
                    this.setContent(this.index_excel_id, this.iframe);
                }
            }
        });
    /*影片嵌入 end*/

    /*最新消息 start*/
        var news_ADV = {
            online: +"{{$data['index_online']['block_news']}}"
        }
        var news_ADVM = new Vue({
            el: '#news_ADV',
            data: news_ADV,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block_news: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /*最新消息 end*/

    /* EDM start*/
        var edm = {
            online: +"{{$data['index_online']['block_edm']}}",
            edm_id: "{{$data['index_excel'][36]['data3']}}",
            index_excel_id: 37
        }
        var edm = new Vue({
            el: '#edm',
            data: edm,
            methods:{
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block_edm: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                },
                saveId: function () {
                    // console.log(this.index_excel_id, this.edm_id);
                    this.setContent(this.index_excel_id, this.edm_id);
                }
            }
        });
    /* EDM end*/

    /* 多廣告 start */
        var threeADVCtrl = {
            online: +"{{$data['index_online']['block3']}}"
        }
        var threeADVCtrlVM = new Vue({
            el: '#threeADVCtrl',
            data: threeADVCtrl,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block3: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                    threeADVCtrlHiddenHint.hidden = this.online;
                }
            }
        });

        var threeADVCtrlHiddenHint = {
            hidden: +"{{$data['index_online']['block3']}}"
        }
        var threeADVCtrlHiddenHintVM = new Vue({
            el: '#threeADVCtrlHiddenHint',
            data: threeADVCtrlHiddenHint
        });
    /* 多廣告 end */

    /*限時搶購 start*/
        // 跳出視窗
        var changeLimitBox = {
            visibility:　false,
            product_id1: "", product_id2: "",
            product_id3: "", product_id4: "",
            product_id5: "", product_id6: "",
            product_id7: "", product_id8: "",
            product_id9: "", product_id10: "",
            tableName:"" ,caller: null,
            start: 0, end: 0
        }
        var changeLimitBoxVM = new Vue({
            el: '#changeLimitBox', 
            data: changeLimitBox,
            methods: {
                setHidden: function () {
                    maskVM.setHidden();
                },
                productAjaxSubmit: function () {
                    newData = {
                        tableName: this.tableName, 
                        product_id1: this.product_id1, product_id2: this.product_id2, 
                        product_id3: this.product_id3, product_id4: this.product_id4, 
                        product_id5: this.product_id5, product_id6: this.product_id6, 
                        product_id7: this.product_id7, product_id8: this.product_id8, 
                        product_id9: this.product_id9, product_id10: this.product_id10,
                    }
                    this.setProduct(newData);
                },
                timeAjaxSubmit: function () {
                    this.setTimeRange(this.start, this.end);
                },
                updateCallerData: function () {
                    this.caller.product_id1 = this.product_id1; this.caller.product_id6 = this.product_id6;
                    this.caller.product_id2 = this.product_id2; this.caller.product_id7 = this.product_id7;
                    this.caller.product_id3 = this.product_id3; this.caller.product_id8 = this.product_id8;
                    this.caller.product_id4 = this.product_id4; this.caller.product_id9 = this.product_id9;
                    this.caller.product_id5 = this.product_id5; this.caller.product_id10 = this.product_id10;

                },
                updateCallerTime: function () {
                    this.caller.start = this.start; 
                    this.caller.end = this.end;
                    // $('#changeLimitBox').modal('hide');

                },
                noTimeLimit: function () {
                    this.end = '1970-01-01';
                    this.timeAjaxSubmit();
                },
                hasTimeLimit: function () {
                    this.end = this.formatDate(new Date());
                }
            }
        });

        // 表格
        var fifthADV = {
            product_id1:"{{$data['time_product'][0]['product_id']}}",
            product_id2:"{{$data['time_product'][1]['product_id']}}",
            product_id3:"{{$data['time_product'][2]['product_id']}}",
            product_id4:"{{$data['time_product'][3]['product_id']}}",
            product_id5:"{{$data['time_product'][4]['product_id']}}",
            product_id6:"{{$data['time_product'][5]['product_id']}}",
            product_id7:"{{$data['time_product'][6]['product_id']}}",
            product_id8:"{{$data['time_product'][7]['product_id']}}",
            product_id9:"{{$data['time_product'][8]['product_id']}}",
            product_id10:"{{$data['time_product'][9]['product_id']}}",
            start:"{{date('Y-m-d',$data['index_excel'][31]['data2'])}}",
            end:"{{date('Y-m-d',$data['index_excel'][31]['data3'])}}",
            tableName:"time_product"
        }
        var fifthADVVM = new Vue({
            el: '#fifthADV',
            data: fifthADV,
            methods: {
                openBox: function () {
                    changeLimitBox.product_id1 = this.product_id1;
                    changeLimitBox.product_id2 = this.product_id2;
                    changeLimitBox.product_id3 = this.product_id3;
                    changeLimitBox.product_id4 = this.product_id4;
                    changeLimitBox.product_id5 = this.product_id5;
                    changeLimitBox.product_id6 = this.product_id6;
                    changeLimitBox.product_id7 = this.product_id7;
                    changeLimitBox.product_id8 = this.product_id8;
                    changeLimitBox.product_id9 = this.product_id9;
                    changeLimitBox.product_id10 = this.product_id10;
                    changeLimitBox.start = this.start;
                    changeLimitBox.end = this.end;
                    changeLimitBox.tableName = this.tableName;
                    changeLimitBox.visibility = true;
                    changeLimitBox.caller = this;
                    mask.visibility = true;
                }
            }
        });
    /*限時搶購 end*/

    /* 橫幅廣告-1 start */
        var SixthADV = {
            link: "{{$data['index_excel'][11]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][11]['data1']}}",
            online: +"{{$data['index_online']['block4']}}"
        }
        var SixthADVVM = new Vue({
            el: '#SixthADV',
            data: SixthADV,
            methods: {
                openBox: function () {
                    changeImageBox.id = 12;
                    changeImageBox.width = 1410;
                    changeImageBox.height = 470;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block4: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /* 橫幅廣告-1 end */

        
    /*人氣商品 start*/
        var seventhADV = {
            product_id1:"{{$data['hot_product'][0]['product_id']}}",
            product_id2:"{{$data['hot_product'][1]['product_id']}}",
            product_id3:"{{$data['hot_product'][2]['product_id']}}",
            product_id4:"{{$data['hot_product'][3]['product_id']}}",
            product_id5:"{{$data['hot_product'][4]['product_id']}}",
            product_id6:"{{$data['hot_product'][5]['product_id']}}",
            product_id7:"{{$data['hot_product'][6]['product_id']}}",
            product_id8:"{{$data['hot_product'][7]['product_id']}}",
            product_id9:"{{$data['hot_product'][8]['product_id']}}",
            product_id10:"{{$data['hot_product'][9]['product_id']}}",
            tableName:"hot_product",
            online: +"{{$data['index_online']['block5']}}"
        }
        var seventhADVVM = new Vue({
            el: '#seventhADV',
            data: seventhADV,
            methods: {
                openBox: function () {
                    changeProductBox.product_id1 = this.product_id1;
                    changeProductBox.product_id2 = this.product_id2;
                    changeProductBox.product_id3 = this.product_id3;
                    changeProductBox.product_id4 = this.product_id4;
                    changeProductBox.product_id5 = this.product_id5;
                    changeProductBox.product_id6 = this.product_id6;
                    changeProductBox.product_id7 = this.product_id7;
                    changeProductBox.product_id8 = this.product_id8;
                    changeProductBox.product_id9 = this.product_id9;
                    changeProductBox.product_id10 = this.product_id10;
                    changeProductBox.tableName = this.tableName;
                    changeProductBox.visibility = true;
                    changeProductBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block5: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /*人氣商品 end*/


    /*圖文廣告 start*/
        var eighthADVCtrl = {
            online: +"{{$data['index_online']['block12']}}"
        }
        var eighthADVCtrlVM = new Vue({
            el: '#eighthADVCtrl',
            data: eighthADVCtrl,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block12: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                    eighthADVCtrlHiddenHintVM.hidden = this.online;
                    eighthADVLeftVM.online = this.online;
                    eighthADVTextVM.online = this.online;
                }
            }
        });

        var eighthADVLeft = {
            link: "{{$data['index_excel'][12]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][12]['data1']}}",
            online: +"{{$data['index_online']['block12']}}"
        }
        var eighthADVLeftVM = new Vue({
            el: '#eighthADVLeft',
            data: eighthADVLeft,
            methods: {
                openBox: function () {
                    changeImageBox.id = 13;
                    changeImageBox.width = 690;
                    changeImageBox.height = 485;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                }
            }
        });

        var eighthADVText = {
            title: "{{$data['index_excel'][13]['data3']}}",
            content: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $data['index_excel'][14]['data3']); ?>",
            id1: "{{$data['index_excel'][15]['data3']}}",
            id2: "{{$data['index_excel'][16]['data3']}}",
            online: +"{{$data['index_online']['block12']}}"
        }
        var eighthADVTextVM = new Vue({
            el: '#eighthADVText',
            data: eighthADVText,
            methods: {
                ajaxTitle: function () {
                    this.setText(14, this.title);
                },
                ajaxContent: function () {
                    this.setText(15, this.content.replace(/\n/g, '<br>'));
                },
                ajaxId1: function () {
                    this.setText(16, this.id1);
                },
                ajaxId2: function () {
                    this.setText(17, this.id2);
                },
            }
        });

        var eighthADVCtrlHiddenHint = {
            hidden: +"{{$data['index_online']['block12']}}"
        }
        var eighthADVCtrlHiddenHintVM = new Vue({
            el: '#eighthADVCtrlHiddenHint',
            data: eighthADVCtrlHiddenHint
        });
    /*圖文廣告 end*/



    /*店長推薦 start*/
        var ninthADV = {
            product_id1:"{{$data['recommend_product'][0]['product_id']}}",
            product_id2:"{{$data['recommend_product'][1]['product_id']}}",
            product_id3:"{{$data['recommend_product'][2]['product_id']}}",
            product_id4:"{{$data['recommend_product'][3]['product_id']}}",
            product_id5:"{{$data['recommend_product'][4]['product_id']}}",
            product_id6:"{{$data['recommend_product'][5]['product_id']}}",
            product_id7:"{{$data['recommend_product'][6]['product_id']}}",
            product_id8:"{{$data['recommend_product'][7]['product_id']}}",
            product_id9:"{{$data['recommend_product'][8]['product_id']}}",
            product_id10:"{{$data['recommend_product'][9]['product_id']}}",
            tableName:"recommend_product",
            online: +"{{$data['index_online']['block6']}}"
        }
        var ninthADVVM = new Vue({
            el: '#ninthADV',
            data: ninthADV,
            methods: {
                openBox: function () {
                    changeProductBox.product_id1 = this.product_id1;
                    changeProductBox.product_id2 = this.product_id2;
                    changeProductBox.product_id3 = this.product_id3;
                    changeProductBox.product_id4 = this.product_id4;
                    changeProductBox.product_id5 = this.product_id5;
                    changeProductBox.product_id6 = this.product_id6;
                    changeProductBox.product_id7 = this.product_id7;
                    changeProductBox.product_id8 = this.product_id8;
                    changeProductBox.product_id9 = this.product_id9;
                    changeProductBox.product_id10 = this.product_id10;
                    changeProductBox.tableName = this.tableName;
                    changeProductBox.visibility = true;
                    changeProductBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block6: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /*店長推薦 end*/


    /*特價商品 start*/
        var spe_ADV = {
            tableName:"spe_price_product",
            online: +"{{$data['index_online']['block_spe_price']}}"
        }
        var spe_ADVM = new Vue({
            el: '#spe_ADV',
            data: spe_ADV,
            methods: {
                openBox: function () {
                    changeProductBox.tableName = this.tableName;
                    changeProductBox.visibility = true;
                    changeProductBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block_spe_price: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /*特價商品 end*/

		

    /*圖文廣告B start
        var tenthADVCtrl = {
            online: +"{{$data['index_online']['block7']}}"
        }
        var tenthADVCtrlVM = new Vue({
            el: '#tenthADVCtrl',
            data: tenthADVCtrl,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block7: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                    tenthADVCtrlHiddenHintVM.hidden = this.online;
                    tenthADVRightVM.online = this.online;
                    tenthADVText.online = this.online;
                }
            }
        });

        var tenthADVRight = {
            link: "{{$data['index_excel'][19]['data2']}}",
            src: "__UPLOAD__{{$data['index_excel'][19]['data1']}}",
            online: +"{{$data['index_online']['block7']}}"

        }
        var tenthADVRightVM = new Vue({
            el: '#tenthADVRight',
            data: tenthADVRight,
            methods: {
                openBox: function () {
                    changeImageBox.id = 20;
                    changeImageBox.width = 700;
                    changeImageBox.height = 475;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                }
            }
        });

        var tenthADVText = {
            title: "{{$data['index_excel'][17]['data3']}}",
            content: "<?php echo preg_replace('/<br\\s*?\/??>/i','\n', $data['index_excel'][18]['data3']); ?>",
            url: "{{$data['index_excel'][20]['data2']}}",
            online: +"{{$data['index_online']['block7']}}"
        }
        var tenthADVTextVM = new Vue({
            el: '#tenthADVText',
            data: tenthADVText,
            methods: {
                ajaxTitle: function () {
                    this.setText(18, this.title);
                },
                ajaxContent: function () {
                    this.setText(19, this.content.replace(/\n/g, '<br>'));
                    //console.log(this.content.replace(/\n/g, '<br>'));
                },
                ajaxUrl: function () {
                    //console.log(this.url);
                    this.setLink(21, this.url);
                }
            }
        });
        var tenthADVCtrlHiddenHint = {
            hidden: +"{{$data['index_online']['block7']}}"
        }
        var tenthADVCtrlHiddenHintVM = new Vue({
            el: '#tenthADVCtrlHiddenHint',
            data: tenthADVCtrlHiddenHint
        });*/
    /*圖文廣告B end


    /* 橫幅廣告-2 start */
        var new_tenthADV = {
            link: "{{$data['index_excel'][32]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][32]['data1']}}",
            online: +"{{$data['index_online']['block7_new']}}"
        }
        var new_tenthADVVM = new Vue({
            el: '#new_tenthADV',
            data: new_tenthADV,
            methods: {
                openBox: function () {
                    changeImageBox.id = 33;
                    changeImageBox.width = 1920;
                    changeImageBox.height = 635;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block7_new: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /* 橫幅廣告-2 end */


    /*即期良品 start*/
        var eleventhADV = {
            product_id1:"{{$data['expiring_product'][0]['product_id']}}",
            product_id2:"{{$data['expiring_product'][1]['product_id']}}",
            product_id3:"{{$data['expiring_product'][2]['product_id']}}",
            product_id4:"{{$data['expiring_product'][3]['product_id']}}",
            product_id5:"{{$data['expiring_product'][4]['product_id']}}",
            product_id6:"{{$data['expiring_product'][5]['product_id']}}",
            product_id7:"{{$data['expiring_product'][6]['product_id']}}",
            product_id8:"{{$data['expiring_product'][7]['product_id']}}",
            product_id9:"{{$data['expiring_product'][8]['product_id']}}",
            product_id10:"{{$data['expiring_product'][9]['product_id']}}",
            tableName:"expiring_product",
            online: +"{{$data['index_online']['block8']}}"
        }
        var eleventhADVVM = new Vue({
            el: '#eleventhADV',
            data: eleventhADV,
            methods: {
                openBox: function () {
                    changeProductBox.product_id1 = this.product_id1;
                    changeProductBox.product_id2 = this.product_id2;
                    changeProductBox.product_id3 = this.product_id3;
                    changeProductBox.product_id4 = this.product_id4;
                    changeProductBox.product_id5 = this.product_id5;
                    changeProductBox.product_id6 = this.product_id6;
                    changeProductBox.product_id7 = this.product_id7;
                    changeProductBox.product_id8 = this.product_id8;
                    changeProductBox.product_id9 = this.product_id9;
                    changeProductBox.product_id10 = this.product_id10;
                    changeProductBox.tableName = this.tableName;
                    changeProductBox.visibility = true;
                    changeProductBox.caller = this;
                    mask.visibility = true;
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block8: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                }
            }
        });
    /*即期良品 end*/


    /*分館廣告 start*/
        var product = "{{$data['product']}}";
        product = product.replace(/&quot;/g,'"');
        product = JSON.parse(product);
        //console.info(product);
        var twelfthADV = {
            list: product,
            online: +"{{$data['index_online']['block9']}}"
        }
        var twelfthADVVM = new Vue({
            el: "#twelfthADVVM",
            data: twelfthADV,
            methods:{
                endDrag: function () {
                    var idArray = []
                    this.list.forEach(function(element) {
                        idArray.push(element.id);
                    });
                    //console.log(idArray);
                    twelfthADVVM.setProductOrder(idArray);
                },
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block9: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                },
                onChange: function () {
                    this.list.forEach(function(element) {
                        twelfthADVVM.setProductOnline(element.id, element.online);
                        console.log(element);
                    });
                }
            }
        });
    /*分館廣告 end*/


    /*推薦圖文 start
        var thirteenthADVCtrl = {
            online: +"{{$data['index_online']['block10']}}"
        }
        var thirteenthADVCtrlVM = new Vue({
            el: '#thirteenthADVCtrl',
            data: thirteenthADVCtrl,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block10: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                    thirteenthADVCtrlHiddenHintVM.hidden = this.online;
                    thirteenthRowLeftVM.online = this.online;
                    thirteenthRowRightVM.online = this.online;
                }
            }
        });

        var thirteenthRowLeft = {
            link: "{{$data['index_excel'][21]['data2']}}",
            src: "__UPLOAD__{{$data['index_excel'][21]['data1']}}",
            title: "{{$data['index_excel'][23]['data3']}}",
            content: "{{preg_replace('/<br\\s*?\/??>/i','\n', $data['index_excel'][24]['data3'])}}",
            online: +"{{$data['index_online']['block10']}}",
            location: 'left'
        }
        var thirteenthRowLeftVM = new Vue({
            el: '#thirteenthRowLeft',
            data: thirteenthRowLeft,
            methods: {
                openBox: function () {
                    changeImgtextBox.link = this.link;
                    changeImgtextBox.src = this.src;
                    changeImgtextBox.title = this.title;
                    changeImgtextBox.content = this.content;
                    changeImgtextBox.location = this.location;
                    changeImgtextBox.visibility = true;
                    changeImgtextBox.caller = this;
                    mask.visibility = true;
                }
            }
        });

        var thirteenthRowRight = {
            link: "{$data['index_excel'][22]['data2']}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][22]['data1']}}",
            title: "{{$data['index_excel'][25]['data3']}}",
            content: "{{preg_replace('/<br\\s*?\/??>/i','\n', $data['index_excel'][26]['data3'])}}",
            online: +"{{$data['index_online']['block10']}}",
            location: 'right'
        }
        var thirteenthRowRightVM = new Vue({
            el: '#thirteenthRowRight',
            data: thirteenthRowRight,
            methods: {
                openBox: function () {
                    changeImgtextBox.link = this.link;
                    changeImgtextBox.src = this.src;
                    changeImgtextBox.title = this.title;
                    changeImgtextBox.content = this.content;
                    changeImgtextBox.location = this.location;                
                    changeImgtextBox.visibility = true;
                    changeImgtextBox.caller = this;
                    mask.visibility = true;
                }
            }
        });
        var thirteenthADVCtrlHiddenHint = {
            hidden: +"{{$data['index_online']['block10']}}"
        }
        var thirteenthADVCtrlHiddenHintVM = new Vue({
            el: '#thirteenthADVCtrlHiddenHint',
            data: thirteenthADVCtrlHiddenHint
        });
    推薦圖文 end*/


    /* 三幅廣告 start*/
        var new_thirteenthADVCtrl = {
            online: +"{{$data['index_online']['block10_new']}}"
        }
        var new_thirteenthADVCtrlVM = new Vue({
            el: '#new_thirteenthADVCtrl',
            data: new_thirteenthADVCtrl,
            methods: {
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block10_new: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                    new_thirteenthADVLeftVM.online = this.online;
                    new_thirteenthADVCenterVM.online = this.online;
                    new_thirteenthADVRightVM.online = this.online;
                    new_thirteenthADVCtrlHiddenHint.hidden = this.online;
                }
            }

        });

        var new_thirteenthADVLeft = {
            link: "{{$data['index_excel'][33]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][33]['data1']}}",
            online: +"{{$data['index_online']['block10_new']}}"
        }
        var new_thirteenthADVLeftVM = new Vue({
            el: '#new_thirteenthADVLeft',
            data: new_thirteenthADVLeft,
            methods: {
                openBox: function () {
                    changeImageBox.id = 34;
                    changeImageBox.width = 470;
                    changeImageBox.height = 470;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                }
            }
        });

        var new_thirteenthADVCenter = {
            link: "{{$data['index_excel'][34]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][34]['data1']}}",
            online: +"{{$data['index_online']['block10_new']}}"
        }
        var new_thirteenthADVCenterVM = new Vue({
            el: '#new_thirteenthADVCenter',
            data: new_thirteenthADVCenter,
            methods: {
                openBox: function () {
                    changeImageBox.id = 35;
                    changeImageBox.width = 470;
                    changeImageBox.height = 470;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                }
            }

        });

        var new_thirteenthADVRight = {
            link: "{{$data['index_excel'][35]['data2']}}",
            src: "{{__UPLOAD__}}{{$data['index_excel'][35]['data1']}}",
            online: +"{{$data['index_online']['block10_new']}}"
        }
        var new_thirteenthADVRightVM = new Vue({
            el: '#new_thirteenthADVRight',
            data: new_thirteenthADVRight,
            methods: {
                openBox: function () {
                    changeImageBox.id = 36;
                    changeImageBox.width = 470;
                    changeImageBox.height = 470;
                    changeImageBox.link = this.link;
                    changeImageBox.src = this.src;
                    changeImageBox.visibility = true;
                    changeImageBox.caller = this;
                    mask.visibility = true;
                }
            }
        });

        var new_thirteenthADVCtrlHiddenHint = {
            hidden: +"{{$data['index_online']['block10_new']}}"
        }
        var new_thirteenthADVCtrlHiddenHintVM = new Vue({
            el: '#new_thirteenthADVCtrlHiddenHint',
            data: new_thirteenthADVCtrlHiddenHint
        });
    /* 三幅廣告 end*/


    
    /* 頁尾選單 start */
        var footer_nav = {
            nav_other_footer: +"{{$data['index_online']['nav_other_footer']}}",
        }
        var footer_navVM = new Vue({
            el: '#footer_nav',
            data: footer_nav,
            methods: {
                onlineToggle: function (target) {
                    //console.log(this.online);
                    blockData = {
                        id: 1,
                    }
                    blockData[target] = this[target] ? 1 : 0;
                    this.blockCtrl(blockData);
                },
            }
        });
    /* 頁尾選單 end */

    /*頁尾區域文字 start*/
        // 跳出視窗
        var changeTextBox = {
            id: 0,
            content: '',
            title: '',
            visibility:　false,
            caller: null
        }
        var changeTextBoxVM = new Vue({
            el: '#changeTextBox', 
            data: changeTextBox,
            watch: {
                content: function (val) {
                    editor.html(val);
                }
            },
            methods: {
                setHidden: function () {
                    // maskVM.setHidden();
                },
                ajaxSubmit: function () {
                    editor.sync();
                    //console.log(editor.html());
                    this.content = editor.html();
                    this.setContent(this.id, this.content.replace(/\n/g, ''));
                    this.setContent(this.id, this.title, 'data2');
                },
                updateCallerData: function () {
                    switch (this.id) {
                        case 28:
                            this.caller.content1 = this.content;
                            this.caller.title1 = this.title;
                            $('#changeTextBox').modal('hide');
                            break;
                        case 29:
                            this.caller.content2 = this.content;
                            this.caller.title2 = this.title;
                            $('#changeTextBox').modal('hide');
                            break;
                        case 30:
                            this.caller.content3 = this.content;
                            this.caller.title3 = this.title;
                            $('#changeTextBox').modal('hide');
                            break;
                        case 31:
                            this.caller.content4 = this.content;
                            this.caller.title4 = this.title;
                            $('#changeTextBox').modal('hide');
                            break;
                        default:
                            break
                    }
                }
            }
        });

        // 表格
        var fourteenthADV = {
            content1: "{!! addslashes($data['index_excel'][27]['data3']) !!}",
            title1: "{{$data['index_excel'][27]['data2']}}",
            content2: "{!! addslashes($data['index_excel'][28]['data3']) !!}",
            title2: "{{$data['index_excel'][28]['data2']}}",
            content3: "{!! addslashes($data['index_excel'][29]['data3']) !!}",
            title3: "{{$data['index_excel'][29]['data2']}}",
            content4: "{!! addslashes($data['index_excel'][30]['data3']) !!}",
            title4: "{{$data['index_excel'][30]['data2']}}",
            online: + "{{$data['index_online']['block11']}}"
        }
        var fourteenthADVVM = new Vue({
            el: "#fourteenthADV",
            data: fourteenthADV,
            methods:{
                onlineToggle: function () {
                    console.log(this.online);
                    blockData = {
                        id: 1,
                        block11: this.online ? 1 : 0
                    }
                    this.blockCtrl(blockData);
                },
                openBox: function (number) {
                    switch (number) {
                        case 1:
                            changeTextBox.id = 28;
                            break;
                        case 2:
                            changeTextBox.id = 29;
                            break;
                        case 3:
                            changeTextBox.id = 30;
                            break;
                        case 4:
                            changeTextBox.id = 31;
                            break;
                        default:
                            break;
                    }
                    changeTextBox.content = this["content"+number];
                    changeTextBox.title = this["title"+number];

                    changeTextBox.visibility = true;
                    changeTextBox.caller = this;
                    mask.visibility = true;
                }
            }
        });
    /*頁尾區域文字 end*/
    </script>


@endsection