@extends('home.Public.mainTpl')
@section('title'){{Lang::get('個人資訊')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <style>
        .hide{
            display: none;
        }
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('個人資訊')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="titleBox">
                        <div class="title">
                            <h3>{{Lang::get('個人資訊')}}</h3>
                        </div>
                    </div>

                    <div class="memberMiddle memberitems">
                        <div class="row">
                            <div class="form-group col-sm-6 col-12">
                                <label for="" class="col-form-label">{{Lang::get('會員編號')}}</label>
                                <input type="text" class="form-control" placeholder="{{Lang::get('會員編號')}}" value="{{$data['userD']['number']}}" readonly>
                                <!-- add class => form-control-plaintext -->
                            </div>
                            <div class="form-group col-sm-6 col-12">
                                <label for="" class="col-form-label">{{Lang::get('帳號')}}</label>
                                <input type="text" class="form-control" placeholder="{{Lang::get('編號')}}" value="{{$data['userD'][$data['account_column']]}}" readonly>
                            </div>
                        </div>
                        <div id="oldpw" class="row">
                            <div class="form-group col-12">
                                <label for="oldpassword" class="col-form-label">{{Lang::get('舊密碼')}}</label>
                                <input type="password" class="form-control" id="oldpassword" placeholder="{{Lang::get('請先驗證舊密碼')}}">
                            </div>
                            <div class="form-group d-flex justify-content-center">
                                <a class="use-btn" onclick="chpwd();">{{Lang::get('修改密碼')}}</a>
                            </div>
                        </div>
                        <!-- ///////////////////////////////////////////////////////////////////////////// -->
                        <div class="hide" id="resetpw">
                            <form id="setpwform" action="{{url('Member/chpwd')}}" method="post">
                                @csrf
                                <input type="hidden" name="id" value="{{$data['userD']['id']}}"/>
                                <!-- /// -->
                                <div class="row">
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('新密碼')}}</label><span class="smallText">{{Lang::get('密碼需包含英文及數字')}}</span>
                                        <input type="password" class="form-control" name="password">
                                    </div>
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('確認新密碼')}}</label>
                                        <input type="password" class="form-control" name="passwordB">
                                    </div>
                                    <div class="form-group d-flex justify-content-center">
                                        <a class="use-btn" onclick="document.getElementById('setpwform').submit();">{{Lang::get('密碼修改')}}</a>
                                    </div>
                                </div>
                                <!-- /// -->
                            </form>
                        </div>
                        <!-- ///////////////////////////////////////////////////////////////////////////// -->
                        <hr>
                        <div class="" id="resetdata">
                            <form id="setdataform" action="{{url('Member/chdata')}}" method="post" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="id" value="{{$data['userD']['id']}}"/>
                                @if(config('control.control_VipDiscount')==1)
                                <div class="row">
                                    <div class="form-group col-sm-6 col-12">
                                        <h4>{{Lang::get('會員等級')}}：
                                            @if($data['userD']['vip_id']==0)
                                                {{Lang::get('無')}}
                                            @else
                                                {{$data['userD']['vip_name']}}
                                            @endif
                                        </h4>
                                    </div>
                                </div>
                                @endif
                                <div class="top mb-2">
                                    <span class="text-danger">{{Lang::get('填入會員資料，下單時即可快速輸入')}}</span>
                                    <span></span>
                                </div>
                                <div class="row">
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('姓名')}}</label>
                                        <input type="text" class="form-control" name="name" value="{{$data['userD']['name']}}">
                                    </div>
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('信箱')}}</label>
                                        <input type="text" class="form-control" name="email" value="{{$data['userD']['email']}}">
                                    </div>
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('聯絡電話')}}</label>
                                        <input type="text" class="form-control" name="tele" value="{{$data['userD']['tele']}}">
                                    </div>
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('生日')}}</label>
                                        <input type="date" name="birthday" class="form-control" value="{{$data['userD']['birthday']}}" />
                                    </div>
                                    <div class="form-group col-12">
                                        <label for="" class="col-form-label">{{Lang::get('地址')}}</label>
                                        <div class="row addressBox">
                                            <div class="col-sm-6 use-mb">
                                                <div class="d-flex flex-wrap use-row">
                                                    <div class="col-4 use-col">
                                                        <select name="F_I_CNo" id="myCity" class="ui fluid selection dropdown no label">
                                                            @if($data['userD']['F_I_CNo'] == null)
                                                                <option value="">{{Lang::get('請選擇縣市')}}</option>
                                                                @foreach($data['city'] as $vo)
                                                                    <option value="{{$vo['AutoNo']}}">{{$vo['Name']}}</option>
                                                                @endforeach
                                                            @else
                                                                <option value="{{$data['userD']['F_I_CNo']}}">{{$data['userD']['F_I_CNo_Name']}}</option>
                                                                @foreach($data['city'] as $vo)
                                                                    <option value="{{$vo['AutoNo']}}">{{$vo['Name']}}</option>
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    </div>
                                                    <div class="col-4 use-col">
                                                        <select name="F_I_TNo" id="myTown" class="ui fluid selection dropdown no label">
                                                            @if($data['userD']['F_I_TNo'] == null)
                                                                <option value="">{{Lang::get('請選擇鄉鎮區')}}</option>
                                                            @else
                                                                <option value="{{$data['userD']['F_I_TNo']}}">{{$data['userD']['F_I_TNo_Name']}}</option>
                                                            @endif
                                                        </select>
                                                    </div>
                                                    <div class="col-4 use-col">
                                                        <input type="text" class="form-control" placeholder="{{Lang::get('郵遞區號')}}" id="myZip" name="F_S_NH_Zip" size="5" readonly="ture">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6 use-mb">
                                                <!-- <input type="text" class="form-control" placeholder="地址"> -->
                                                @if($data['userD']['F_I_CNo'] == null)
                                                    <input type="text" class="form-control" name="F_S_NH_Address" value="{{$data['userD']['home']}}" placeholder="{{Lang::get('請輸入地址')}}"/>
                                                @else
                                                    <input type="text" class="form-control" name="F_S_NH_Address" value="{{$data['userD']['F_S_NH_Address']}}" placeholder="{{Lang::get('請輸入地址')}}"/>
                                                @endif
                                            </div>
                                            <!-- //////////////////////////////////// -->
                                        </div>
                                    </div>
                                    <!-- 第三方發票開啟設定 -->
                                    @if(config('control.thirdpart_invoice')==1)
                                    <div class="form-group col-12">
                                        <label for="" class="col-form-label">{{Lang::get('載具編號')}}</label>
                                        <input type="text" name="invoice" class="form-control" value="{{$data['userD']['invoice']}}" />
                                    </div>
                                    @endif

                                    @if(config('control.control_platform')==1)
                                        @if($data['userD']['user_type_radio'] == 1 || $data['userD']['user_type'] == 1)
                                            <hr class="col-12">
                                            <div class="form-group col-sm-12 col-12">
                                                <h4>{{Lang::get('會員類型')}}：{{Lang::get('供應商')}}
                                                    (@if($data['userD']['user_type'] == 1)
                                                        {{Lang::get('開通')}}
                                                    @else
                                                        {{Lang::get('未開通')}}
                                                    @endif)
                                                </h4>
                                            </div>
                                            <div class="form-group col-sm-6 col-12">
                                                <label class="col-form-label">{{Lang::get('店鋪名稱')}}</label>
                                                <input type="text" class="form-control" name="shop_name" value="{{$data['userD']['shop_name']}}">
                                            </div>
                                            <div class="form-group col-sm-6 col-12"></div>
                                            <div class="form-group col-sm-6 col-12">
                                                <label class="col-form-label">{{Lang::get('公司登記文件')}}</label>
                                                @if($data['userD']['file_company'])
                                                    <a href="{{__UPLOAD__}}{{$data['userD']['file_company']}}" target="_blank">({{Lang::get('查看')}})</a>
                                                @else
                                                    {{Lang::get('無')}}
                                                @endif
                                                <input type="file" class="form-control" name="file_company">
                                            </div>
                                            <div class="form-group col-sm-6 col-12">
                                                <label class="col-form-label">{{Lang::get('個人身份文件')}}</label>
                                                @if($data['userD']['file_person'])
                                                    <a href="{{__UPLOAD__}}{{$data['userD']['file_person']}}" target="_blank">({{Lang::get('查看')}})</a>
                                                @else
                                                    {{Lang::get('無')}}
                                                @endif
                                                <input type="file" class="form-control" name="file_person">
                                            </div>
                                            <div class="form-group col-sm-12 col-12">
                                                <label class="col-form-label">{{Lang::get('收款帳號')}}</label>
                                                <div class="row m-0">
                                                    <div class="col-sm-2">
                                                        <input type="text" class="form-control" name="bank" placeholder="{{Lang::get('銀行名稱')}}" value="{{$data['userD']['bank']}}">
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <input type="text" class="form-control" name="bank_code" placeholder="{{Lang::get('分行代號')}}" value="{{$data['userD']['bank_code']}}">
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <input type="text" class="form-control" name="bank_account_name" placeholder="{{Lang::get('戶名')}}" value="{{$data['userD']['bank_account_name']}}">
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="bank_account_code" placeholder="{{Lang::get('請輸入銀行帳號')}}" value="{{$data['userD']['bank_account_code']}}">
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endif

                                    <div class="form-group d-flex justify-content-center">
                                        <a class="use-btn"  onclick="javascript:document.getElementById('setdataform').submit();">{{Lang::get('儲存')}}</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <hr>
                        <!-- ///////////////////////////////////////////////////////////////////////////// -->
                        @if((config('extra.social_media.FB_appID') && $data['userD']['FB_id'] =='') || 
                                    (config('extra.social_media.client_id') && $data['userD']['line_id'] =='') || 
                                    (config('extra.social_media.Google_appId') && $data['userD']['gmail'] =='') || 
                                    $data['userD'][$data['account_column']]=='')
                            <div class="row">
                                <div class="col-12 bindingBox">
                                    <div>
                                        <div class="top mb-4">
                                            <span class="text-danger">{{Lang::get('授權綁定')}}：</span>
                                            <span>{{Lang::get('商城帳號綁定第三方帳號，可直接登入商城網站')}}</span>
                                        </div>
                                        <div class="row use-row">
                                            @if(config('extra.social_media.FB_appID') && ($data['userD']['FB_id'] =='' && $data['userD'][$data['account_column']]!=''))
                                                <div class="col-xl-3 col-4 use-col">
                                                    <img src="{{__PUBLIC__}}/img/icon_facebook-en.png" alt="FB" onclick="open_FB();">
                                                </div>
                                            @endif
                                            @if(config('extra.social_media.client_id') && ($data['userD']['line_id'] =='' && $data['userD'][$data['account_column']]!=''))
                                                <div class="col-xl-3 col-4 use-col">
                                                    <img src="{{__PUBLIC__}}/img/icon_line-en.png" alt="LINE" onclick="open_line();">
                                                </div>
                                            @endif
                                            @if(config('extra.social_media.Google_appId') && ($data['userD']['gmail'] =='' && $data['userD'][$data['account_column']]!=''))
                                                <div class="col-xl-3 col-4 use-col">
                                                    <img src="{{__PUBLIC__}}/img/icon_google.png" alt="Google" onclick="GoogleLogin(1);">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!--///////////////////////////////////////////////////////////////////////////// -->
                        @if($data['userD'][$data['account_column']] == '')
                            <div id='Binding'>
                                <div class="top">
                                    <span>{{Lang::get('綁定商城會員')}}</span>
                                </div>
                            </div>
                            <div>
                                <div class="row">
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('帳號')}}</label>
                                        <input type="text" class="form-control" type="email" id="ck_id">
                                    </div>
                                    <div class="form-group col-sm-6 col-12">
                                        <label for="" class="col-form-label">{{Lang::get('密碼')}}</label>
                                        <input class="form-control" type="password" id="ck_pw">
                                    </div>
                                    <div class="form-group col-12 text-center">
                                        @if($data['userD']['gmail'] !='' && $data['userD']['line_id'] =='' && $data['userD']['FB_id'] =='')
                                            <a class="use-btn send_btn text-center" href="javascript:account_open('gmail');">
                                                {{Lang::get('送出')}}
                                            </a>
                                        @elseif($data['userD']['gmail'] =='' && $data['userD']['line_id'] !='' && $data['userD']['FB_id'] =='')
                                            <a class="use-btn send_btn text-center" href="javascript:account_open('line_id');">
                                                {{Lang::get('送出')}}
                                            </a>
                                        @elseif($data['userD']['gmail'] =='' && $data['userD']['line_id'] =='' && $data['userD']['FB_id'] !='')
                                            <a class="use-btn send_btn text-center" href="javascript:account_open('FB_id');">
                                                {{Lang::get('送出')}}
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                        <!-- ///////////////////////////////////////////////////////////////////////////// -->
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <!-- 選擇縣市區 -->
    <script type="text/javascript">
        var CNo= $('#myCity').val();
        if(CNo != ''){
            $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    url: "{{url('Login/town_ajax')}}",
                    cache: false,
                    data:{CNo : CNo},
                    error: function(){
                        alert("{{Lang::get('發生錯誤')}}");
                    },
                    success: function(data){
                        $('#myTown').html(`<option value="{{$data['userD']['F_I_TNo']}}">{{$data['userD']['F_I_TNo_Name']}}</option>`+data);
                        //$('#myZip').val("");//避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
                    }
                });

                var TNo= $('#myTown').val();
                $.ajax({
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    url: "{{url('Login/zip_ajax')}}",
                    cache: false,
                    data:{TNo:TNo},
                    error: function(){
                        alert("{{Lang::get('發生錯誤')}}");
                    },
                    success: function(data){
                        $('#myZip').val(data);
                    }
                });
        }

        //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
        $('#myCity').change(function(){
            var CNo= $('#myCity').val();           
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                url: "{{url('Login/town_ajax')}}",
                cache: false,
                data:{CNo : CNo},
                error: function(){
                    alert("{{Lang::get('發生錯誤')}}");
                },
                success: function(data){
                    $('#myTown').html('<option value="">請選擇鄉鎮區</option>'+data);
                    $('#myZip').val("");//避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
                }
            });
        });

        //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
        $('#myTown').change(function(){
            var TNo= $('#myTown').val();
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                url: "{{url('Login/zip_ajax')}}",
                cache: false,
                data:{TNo:TNo},
                error: function(){
                    alert("{{Lang::get('發生錯誤')}}");
                },
                success: function(data){
                    $('#myZip').val(data);
                }
            });
        });
    </script>

    <script>  
        /*更改密碼*/
        function chpwd(){
            $.ajax({
                url: "{{url('Ajax/chpwd')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id: {{$data['userD']['id']}},
                    pwd: $('#oldpassword').val()
                },
                error: function(xhr) {
                    alert("{{Lang::get('發生錯誤')}}");
                },
                success: function(response) {
                    if(response.status){
                        alert("{{Lang::get('驗證成功')}}");
                        $('#resetpw').removeClass('hide');
                        $('#oldpw').addClass('hide');
                    }else{
                        alert("{{Lang::get('驗證失敗')}}");
                    }
                }
            });
        }

        /*商城帳號綁定FB帳號*/
        function open_FB() {
            FB.login(function (response) {
                //debug用
                console.log(response);
                if (response.status === 'connected') {
                    //user已登入FB
                    //抓userID
                    let FB_ID = response["authResponse"]["userID"];
                    console.log("userID:" + FB_ID);
                    $.ajax({
                        url     : "{{url('Login/fb_open')}}",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-Token': '{{csrf_token()}}' 
                        },
                        type    : 'POST',
                        data : { U3: FB_ID,ig:FB_ID},
                        contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                        success: function(result){
                            if(result.code){
                                alert("{{Lang::get('成功合併，請重新登入')}}");
                                Del_FB_App();
                                location.href="{{url('Login/Logout')}}";
                            }else{
                                alert(result.msg);
                            }
                        }
                    });
                } else {
                    // user FB取消授權
                    alert("{{Lang::get('授權失敗')}}");
                }
            }, { scope: 'public_profile,email' });
        }

        /*商城帳號綁定LINE帳號*/
        function open_line(){
            var URL = 'https://access.line.me/oauth2/v2.1/authorize?';
                URL += 'response_type=code';
                URL += '&client_id={{config('extra.social_media.client_id')}}';//胖胖
                //URL += '&client_id=**********';//傳訊光
                URL += '&redirect_uri={{config('extra.social_media.line_url_open')}}';
                URL += '&state=abcdASDFe';
                URL += '&scope=openid%20profile';
                window.location.href = URL;
        }

        /* 社群帳戶綁定商城帳號 */
        function account_open(aim){
            var id = $('#ck_id').val();
            var pw = $('#ck_pw').val();
            console.log(id);
            console.log(pw);
            if( id == '' || pw ==''){
                alert("{{Lang::get('資料不完整')}}");
            }else{
                var url = "{{url('Login/account_open')}}";
                if(aim == 'gmail'){
                    var user_new = "{{$data['userD']['gmail']}}";
                }else if(aim == 'FB_id'){
                    var user_new = "{{$data['userD']['FB_id']}}";
                }else{
                    var user_new = "{{$data['userD']['line_id']}}";
                }

                $.ajax({
                    url: url,
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': '{{csrf_token()}}' 
                    },
                    datatype: 'json',
                    data: { id:id,pw:pw,user_new:user_new,aim:aim},
                    error: function (xhr) {
                        alert("{{Lang::get('操作失敗')}}");
                        console.error(xhr);
                    },
                    success: function (result) {
                        if(result.code){
                            alert("{{Lang::get('成功合併，請重新登入')}}");
                            location.href="{{url('Login/Logout')}}";
                        }else{
                            alert(result.msg);
                        }
                    },
                });
            }
        }
    </script>
@endsection

