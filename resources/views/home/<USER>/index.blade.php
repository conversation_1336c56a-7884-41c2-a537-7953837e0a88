@extends('home.Public.mainTpl')
@section('title'){{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- Banner start 輪播圖片-->
@if($data['index_online']['block2'] == 1)
    <section class="bannerOwlCarouselRow">
        <div class="owl-carousel proImgCarousel owl-theme">
            @if(empty($data['slideshow']) == false)
            @foreach ($data['slideshow'] as $slider)
            <div class="item">
                <a href="{{$slider['link']}}"> <!-- style="background-image: url({{__PUBLIC__}}{$slider['pic']});"-->
                    <img class="w-100" src="{{__PUBLIC__}}{{$slider['pic']}}">
                    <h2>{{$slider['title']}}</h2>
                </a>
            </div>
            @endforeach
            @endif
        </div>
    </section>
@endif
<!-- Banner end -->

<!-- movie start -->
@if($data['index_online']['block_iframe'] == 1)
    @if($data['index_excel'][37]['data3'])
        <div class="mov">
            {!! $data['index_excel'][37]['data3'] !!}
        </div>
    @endif
@endif
<!-- moviet end -->

<!-- announcement start -->
@if($data['index_online']['block_news'] == 1)
    <section class="announcement-fluid">
        @include('home.Public.newsLink')
    </section>
@endif
<!-- announcement end -->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- EDM start EDM-->
@if($data['index_online']['block_edm'] == 1)
    @if(config('control.control_index_edm')==1)
    <div id="rwd_edm_home" class="rwd_edm container"></div>
    @endif
@endif
<!-- EDM end EDM-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- top Ad start 抬頭廣告-->
@if($data['index_online']['block1'] == 1)
<section class="containerlookUpAdRow container mb-2">
    <a href="{{$data['index_excel'][7]['data2'] ?? '#!'}}">
        <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][7]['data1']}}">
    </a>
</section>
@endif
<!--  top Ad end -->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- three carousel ad start-->
@if($data['index_online']['block3'] == 1)
    <!-- 輪播
    <section class="container threeSquareAdRow">
        <div class="owl-carousel proImgCarousel owl-theme">
            @foreach ($data['index_ad'] as $ad)
                <div class="item">
                    <a href="{$ad['url']}">
                        <img class="w-100" src="{{__PUBLIC__}}{$ad['pic']}">
                    </a>
                </div>
            @endforeach
        </div>
    </section> -->
    <section class="container threeSquareAdRow">
        <div class="row">
            @if(empty($data['index_ad']) == false)
            @foreach($data['index_ad'] as $ad)
                <div class="item mb-2">
                    <a @if ($ad['url']) href="{{$ad['url']}}" @endif>
                        <img class="w-100" src="{{__PUBLIC__}}{{$ad['pic']}}">
                    </a>
                </div>
            @endforeach
            @endif
        </div>
    </section>
@endif
<!-- three carousel ad end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- Limited Time start-->
@if($data['time_block'] == 1)
    @if(config('control.control_time_limit_prod')==1)
    <section class="timerProducts productListRow ">
        <div class="container">
            <div class="titleBox">
                <div class="title">
                    <h3><i class="bi bi-clock"></i> {{Lang::get('限時搶購')}}</h3>
                </div>
                @if($data['time_range'] =='1')
                <p class="reciprocalBox">
                    {{Lang::get('倒數')}}
                    <span class="colon">:</span>
                    <span id="hr" class="num">{{$data['reciprocalTime']['hr']}}</span>
                    <span class="colon">:</span>
                    <span id="mn" class="num">{{$data['reciprocalTime']['mn']}}</span>
                    <span class="colon">:</span>
                    <span id="sc" class="num">{{$data['reciprocalTime']['sc']}}</span>
                </p>
            </div>
            <p class="activityTime">{{Lang::get('活動時間')}}<span class="colon">:</span>{{$data['startTime']}}~{{$data['endTime']}}</p>
            <script>
                setInterval(function () {
                    var sc = $('#sc').text();
                    var mn = $('#mn').text();
                    var hr = $('#hr').text();
                    if (--sc == -1) {
                        sc = 59;
                        if (--mn == -1) {
                            mn = 59;
                            if (--hr == -1) {
                                hr == 0;
                                mn == 0;
                                sc == 0;
                                $('.index_sec_Stime').hide();
                            }
                        }
                    }
                    $('#sc').text(sc);
                    $('#mn').text(mn);
                    $('#hr').text(hr);
                }, 1000);
            </script>
            @endif
            <div class="proBox">
                <div class="owl-carousel proImgCarousel owl-theme">
                    @if(empty($data['time_product']) == false)
                    @foreach($data['time_product'] as $productvo)
                    <div class="item cursor-pointer" onclick='location.href="{{url('Product/productinfo')}}?id={{$productvo['id']}}";'>
                        <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo['pic']}});">
                            @if(!empty($productvo[$productvo['id']]['coupon_button']))
                            <a class="couponLabel" {{$productvo[ $productvo['id'] ]['coupon_button'] ?? '' }}>
                                <p>
                                    <span>{{Lang::get('優惠券')}}</span>
                                </p>
                            </a>
                            @endif
                        </div>
                        <div class="textBox" onclick='location.href="{{url('Product/productinfo')}}?id={{$productvo['id']}}";'>
                            <h3>{{$productvo['title']}} 
                                @if(!empty($productvo['act_data']['link']))
                                <span class="activityLabel">
                                    <a {{$productvo['act_data']['link'] ?? '' }}>{{$productvo['act_data']['type_name']}}</a>
                                </span>
                                @endif
                            </h3>
                            <div class="priceBox">
                                <span class="originalPrice">{{$productvo['show'][0]['originalPrice']}}</span>
                                <span class="offerPrice">{!! $productvo['show'][0]['offerPrice'] !!}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    @endif
                </div>
            </div>
        </div>
        
    </section>
    @endif
@endif
<!-- Limited Time end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- Banner Advertising start-->
@if($data['index_online']['block4'] == 1)
<section class="container bannerAdFrame">
    <a href="{{$data['index_excel'][11]['data2']}}">
        <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][11]['data1']}}">
    </a>
</section>
@endif
<!-- Banner Advertising end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- Popularity start 人氣商品-->
@if($data['index_online']['block5'] == 1)
    @if(empty(config('control.close_function_current')['標籤設定']))
    <section class="productListRow hotsale">
        <div class="titleBox">
            <div class="title">
                <h3> <span>{{$data['tag'][0]['name']}}</span></h3>
            </div>
        </div>
        <div class="proBox">
            <div class="owl-carousel proImgCarousel owl-theme">
                @if(!empty($data['hot_product']))
                @foreach($data['hot_product'] as $productvo)
                <div class="item position-relative">
                    @if(isset($data['productvo']['id']['coupon_button']))
                        <a class="couponLabel" {{ ($productvo['id']['coupon_button'] ) ?? '' }}>
                            <p><span>{{Lang::get('優惠券')}}</span></p>
                        </a>
                    @endif
                    <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                        <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo['pic']}});"></div>
                    </a>
                    <div class="textBox">
                        <h3>
                            <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                                {{ $productvo['title']}}
                            </a>
                            @if(isset($productvo['act_data']['link']))
                                <span class="activityLabel">
                                    <a {{ ($productvo['act_data']['link']) ?? '' }}>{{ $productvo['act_data']['type_name']}}</a>
                                </span>
                            @endif
                        </h3>
                        <div class="priceBox">
                            <span class="originalPrice">{{$productvo['show'][0]['originalPrice']}}</span>
                            <span class="offerPrice">{!! $productvo['show'][0]['offerPrice'] !!}</span>
                        </div>
                    </div>
                </div>
                @endforeach
                @endif
            </div>
        </div>
    </section>
    @endif
@endif
<!-- Popularity end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- recommend -->
@if($data['index_online']['block12'] == 1)
<section class="recommendRow">
    <div class="container">
        <div class="titleBox">
            <div class="title">
                <h3>
                    <span>{{Lang::get('推薦商品')}}</span>
                </h3>
            </div>
        </div>
        <div class="contentBox">
            <div class="row">
                <div class="col-md-6">
                    <div class="row ">
                        <a href="{{$data['index_excel'][12]['data2']}}" class="mainPro">
                            <div class="col-md-12 ">
                                <div class="proImg" style="background-image: url({{__PUBLIC__}}/{{$data['index_excel'][12]['data1']}});"></div>
                            </div>
                            <div class="col-md-12 ">
                                <div class="proIntro">
                                    <h3 class="subtitle">{{$data['index_excel'][13]['data3']}}</h3>
                                    <p class="txt">{{$data['index_excel'][14]['data3']}}</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="recommend-pro">
                        <div class="item">
                            <a href="{{url('Product/productinfo')}}?id={{($data['index_excel'][15]['product']['id']) ?? ''}}">
                                @if ($data['index_excel'][15]['product'])
                                <div class="img" style="background-image: url({{__PUBLIC__}}/{{$data['index_excel'][15]['product']['pic']}});"></div>
                                @else
                                <div class="img" style="background-image: url('');"></div>
                                @endif
                                <div class="pro-info">
                                    <h3>{{($data['index_excel'][15]['product']['title']) ?? ''}}</h3>
                                    <p>{{($data['index_excel'][15]['product']['subtitle']) ?? ''}}</p>
                                    <div class="priceBox">
                                        <span class="offerPrice">
                                            {!! ($data['index_excel'][15]['product']['has_price']) ?? '' !!}
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div class="item">
                            <a href="{{url('Product/productinfo')}}?id={{($data['index_excel'][16]['product']['id']) ?? ''}}">
                                @if ($data['index_excel'][16]['product'])
                                <div class="img" style="background-image: url({{__PUBLIC__}}/{{$data['index_excel'][16]['product']['pic']}});"></div>
                                @else
                                <div class="img" style="background-image: url('');"></div>
                                @endif
                                <div class="pro-info">
                                    <h3>{{($data['index_excel'][16]['product']['title']) ?? ''}}</h3>
                                    <p>{{($data['index_excel'][16]['product']['subtitle']) ?? ''}}</p>
                                    <div class="priceBox">
                                        <span class="offerPrice">
                                            {!! ($data['index_excel'][16]['product']['has_price']) ?? '' !!}
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                       
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</section>
@endif
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- Popularity 店長推薦-->
@if($data['index_online']['block6'] == 1)
    @if(empty(config('control.close_function_current')['標籤設定']))
    <section class="productListRow sale-Pro">
        <div class="container">
            <div class="titleBox">
                <div class="title">
                    <h3><span>{{$data['tag'][1]['name']}}</span></h3>
                </div>
            </div>
            <div class="proBox">
                <div class="owl-carousel proImgCarousel owl-theme">
                    @foreach($data['recommend_product'] as $productvo)
                    <div class="item position-relative">
                        @if(!empty($productvo[ $productvo['id'] ]['coupon_button']))
                            <a class="couponLabel" {{$productvo[ $productvo['id'] ]['coupon_button'] ?? ''}}>
                                <p><span>{{Lang::get('優惠券')}}</span></p>
                            </a>
                        @endif
                        <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                            <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo['pic']}});"></div>
                        </a>
                        <div class="textBox">
                            <h3>
                                <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                                    {{$productvo['title']}}
                                </a>
                                @if(!empty($productvo['act_data']['link']))
                                    <span class="activityLabel">
                                        <a {{$productvo['act_data']['link'] ?? ''}}>{{$productvo['act_data']['type_name']}}</a>
                                    </span>
                                @endif
                            </h3>
                            <div class="priceBox">
                                <span class="originalPrice">{{$productvo['show'][0]['originalPrice']}}</span>
                                <span class="offerPrice">{!! $productvo['show'][0]['offerPrice'] !!}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif
@endif

<!-- 特價商品 -->
@if($data['index_online']['block_spe_price'] == 1)
    @if(empty(config('control.close_function_current')['標籤設定']) && config('control.control_sepc_price') == 1)
    <section class="productListRow offer-Pro">
        <div class="container">
            <div class="titleBox">
                <div class="title">
                    <h3><span>{{$data['tag'][3]['name']}}</span> </h3>
                </div>
            </div>
            <div class="proBox">
                <div class="proImgCarousel owl-theme row">
                    @foreach($data['spe_price_product'] as $productvo)
                    <div class="owl-item col-xl-3 col-lg-4 col-md-6 col-6 mb-3">
                        <div class="item">
                            <div class="position-relative">
                                @if(isset($data['productvo'][$productvo['id']]['coupon_button']))
                                <a class="couponLabel" {{$productvo[$productvo['id']]['coupon_button'] ?? ''}}>
                                    <p><span>{{Lang::get('優惠券')}}</span></p>
                                </a>
                                @endif
                                <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                                    <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo['pic']}});"></div>
                                </a>
                            </div>
                            <div class="textBox">
                                <h3>
                                    <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                                        {{$productvo['title']}}
                                    </a>
                                    @if(isset($productvo['act_data']['link']))
                                    <span class="activityLabel">
                                        <a {{$productvo['act_data']['link'] ?? ''}}>
                                        {{$productvo['act_data']['type_name']}}</a>
                                    </span>
                                    @endif
                                </h3>
                                <div>
                                    <div class="priceBox">
                                        <span class="originalPrice">{{$productvo['show'][0]['originalPrice']}}</span>
                                        <span class="offerPrice">{!! $productvo['show'][0]['offerPrice'] !!}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif
@endif
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- bannerAdFull start-->
@if($data['index_online']['block7_new'] == 1)
<section class="bannerAdFull">
    <a href="{{$data['index_excel'][32]['data2']}}">
        <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][32]['data1']}}">
    </a>
</section>
@endif
<!-- bannerAdFull end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- 即期良品 -->
@if($data['index_online']['block8'] == 1)
    @if(!isset(config('control.close_function_current')['標籤設定']))
    <section class="productListRow goods-pro">
        <div class="container">
            <div class="titleBox">
                <div class="title">
                    <h3><span>{{$data['tag'][2]['name']}}</span></h3>
                </div>
            </div>
            <div class="proBox ">
                <div class="owl-carousel proImgCarousel owl-theme">
                    @foreach($data['expiring_product'] as $productvo)
                    <div class="item position-relative">
                        @if(!empty($productvo[$productvo['id']]['coupon_button']))
                            <a class="couponLabel" {{ $productvo[$productvo['id']]['coupon_button'] ?? '' }}>
                                <p><span>{{Lang::get('優惠券')}}</span></p>
                            </a>
                        @endif
                        <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                            <div class="img" style="background-image: url({{__PUBLIC__}}/{{$productvo['pic']}});"></div>
                        </a>
                        <div class="textBox">
                            <h3>
                                <a href="{{url('Product/productinfo')}}?id={{$productvo['id']}}">
                                    {{$productvo['title']}}
                                </a>
                                @if(!empty($productvo['act_data']['link']))
                                    <span class="activityLabel">
                                        <a {{$productvo['act_data']['link'] ?? ''}}>{{$productvo['act_data']['type_name']}}</a>
                                    </span>
                                @endif
                            </h3>
                            <div class="priceBox">
                                <span class="originalPrice">{{$productvo['show'][0]['originalPrice']}}</span>
                                <span class="offerPrice">{!! $productvo['show'][0]['offerPrice'] !!}</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif
@endif
<!-- Spot product end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
<!-- three carousel ad start-->
@if($data['index_online']['block10_new'] == 1)
<section class="container threeSquareAdRow">
    <div class="row">
        <div class="owl-carousel proImgCarousel owl-theme">
            <div class="item">
                <a href="{{$data['index_excel'][33]['data2']}}">
                    <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][33]['data1']}}">
                </a>
            </div>
            <div class="item">
                <a href="{{$data['index_excel'][34]['data2']}}">
                    <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][34]['data1']}}">
                </a>
            </div>
            <div class="item">
                <a href="{{$data['index_excel'][35]['data2']}}">
                    <img class="w-100" src="{{__PUBLIC__}}{{$data['index_excel'][35]['data1']}}">
                </a>
            </div>
        </div>
    </div>
</section>
@endif
<!-- three carousel ad end-->
<!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
@if($data['index_online']['block9'] == 1)
@foreach($data['product'] as $productvo)
    <section class="productBranchRow">
        <div class="container">
            <div class="titleBox">
                <div class="title">
                    <a href="{{url('Product/product')}}?id={{$productvo->id}}">{{$productvo->title}}</a>
                </div>
                <div class="productBranchNav">
                    <div class="btnBox">
                        <a class="left"><i class="bi bi-chevron-left"></i></a>
                        <a class="right"><i class="bi bi-chevron-right"></i></a>
                    </div>
                    <div class="productBranch-viewport">
                        <ul class="productBranch-tab-nav">
                            @foreach($productvo->typeinfo as $subvo)
                            @if($subvo['id'] == '')
                            <li><a href="#!">{{mb_substr($subvo['title'],0,15)}}</a></li>
                            @else
                            <li><a href="{{url('Product/typeinfo')}}?id={{$subvo['id']}}">{{mb_substr($subvo['title'],0,15)}}</a>
                            </li>
                            @endif
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>        
        <div class="aaBox">
            <div class="container">
                <div class="row-custom">
                    <div class="col-md-4-custom">
                        <a href="{{($productvo->index_adv01_link) ?? '#!'}}" class="mainProImg">
                            <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv01_pic}});">
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6-custom">
                        <div class="row">
                            <div class="col-12">
                                <div class="ADimg">
                                    <a href="{{($productvo->index_adv02_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv02_pic}});"></div>
                                    </a>
                                    <a href="{{($productvo->index_adv04_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv04_pic}});"></div>
                                    </a>
                                    <a href="{{($productvo->index_adv06_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv06_pic}});"></div>
                                    </a>

                                    <a href="{{($productvo->index_adv03_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv03_pic}});"></div>
                                    </a>
                                    <a href="{{($productvo->index_adv05_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv05_pic}});"></div>
                                    </a>
                                    <a href="{{($productvo->index_adv07_link) ?? '#!'}}" class="secondaryProImg">
                                        <div style="background-image: url({{__PUBLIC__}}/{{$productvo->index_adv07_pic}});"></div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endforeach
@endif
@endsection
@section("ownJS")
    <script src="{{__PUBLIC__}}/js/bannerOwlCarousel.js"></script>

    <!-- PWA功能 -->
    <script type="text/javascript">
        /*初始化PWA*/
        if('serviceWorker' in navigator){
            navigator.serviceWorker
                .register('sw.js')
                .then(function(){
                    console.log('Service Worker 註冊成功');
                }).catch(function(error) {
                    console.log('Service worker 註冊失敗:', error);
                })
                .then(function(){
                    /*詢問訂閱*/
                    askForNotificationPermission()
                });
        } else {
            console.log('瀏覽器不支援 serviceWorker');
        }

        /*加入主畫面-------------------------------*/
        let deferredPrompt;
        const addToHomeBtn = document.querySelector('#addToHomeBtn');
        if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {
            window.addEventListener('beforeinstallprompt', (e) => {
                // Prevent Chrome 67 and earlier from automatically showing the prompt
                e.preventDefault();
                // Stash the event so it can be triggered later.
                deferredPrompt = e;
                // Update UI to notify the user they can add to home screen
                $('#addToHome_btn').click()

                addToHomeBtn.addEventListener('click', (e) => {
                    // hide our user interface that shows our A2HS button
                    $('#addToHome_btn .close').click();
                    // Show the prompt
                    deferredPrompt.prompt();
                    // Wait for the user to respond to the prompt
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the A2HS prompt');
                        } else {
                            console.log('User dismissed the A2HS prompt');
                        }
                        deferredPrompt = null;
                    });
                });
            });
        }


        /*訂閱-------------------------------*/
        /*編碼轉換(註冊用)*/
        function urlBase64ToUint8Array(base64String) {
            var padding = '='.repeat((4 - base64String.length % 4) % 4);
            var base64 = (base64String + padding)
                .replace(/\-/g, '+')
                .replace(/_/g, '/');

            var rawData = window.atob(base64);
            var outputArray = new Uint8Array(rawData.length);

            for (var i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }
        /*詢問是否訂閱*/
        function askForNotificationPermission() {
            Notification.requestPermission(function(result) {
                // 這裡result只會有兩種結果：一個是用戶允許(granted)，另一個是用戶封鎖(denied)
                console.log('User Choice', result);
                if(result !== 'granted') {
                    console.log('No notification permission granted!');
                } else {
                    configurePushSub();
                    // displayConfirmNotification()
                }
            });
        }
        /*建立註冊資料(確認訂閱)*/
        function configurePushSub() {
            if(!('serviceWorker' in navigator)) {
                return;
            }
            var reg;
            navigator.serviceWorker.ready.then(function(swreg) {
                reg = swreg;
                return swreg.pushManager.getSubscription();
            }).then(function(sub) {
                if(sub === null) {
                    // Create a new subscription
                    var vapidPublicKey = "{{$data['notification_pubkey']}}";
                    var convertedVapidPublicKey = urlBase64ToUint8Array(vapidPublicKey);
                    return reg.pushManager.subscribe({
                            userVisibleOnly: true,
                            applicationServerKey: convertedVapidPublicKey
                        });
                } else {
                    // We have a subscription
                    return sub
                }
            }).then(function(newSub) {
                // console.log(JSON.stringify(newSub))
                return fetch('/index/index/subscripe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-Token': csrf_token,
                    },
                    body: JSON.stringify(newSub)
                })
            }).then(function(res) {
                // console.log(res)
                // if(res.ok) {
                //     displayConfirmNotification();
                // }
            }).catch(function(err) {
                console.log(err);
            })
        }
        /*顯示通知*/
        function displayConfirmNotification() {
            if('serviceWorker' in navigator) {
                var options = {
                    body: "{{Lang::get('您已成功訂閱我們的推播服務')}}!",
                    icon: '/public/static/manifest/favicon.ico-144.png',
                    lang: 'zh-TW',   // BCP 47
                    vibrate: [100, 50, 200],
                    tag: 'confirm-notification',
                    renotify: true,
                    actions: [
                        { action: 'confirm', title: "{{Lang::get('收到')}}"},
                        { action: 'cancel', title: "{{Lang::get('關閉')}}"}
                    ]
                }
                navigator.serviceWorker.ready.then(function(swreg) {
                    swreg.showNotification("{{Lang::get('成功訂閱')}}", options);
                });
            }
        }
    </script>
    
    <script type="text/javascript">
        if(`{{$data['edm_url']}}` && `{{config('control.control_index_edm')}}`=='1' && '{$edmID}'){
            $.ajax({
                url: "//{$edm_url}/api/product_cms/{$edmID}/view",
                type: 'GET',
                datatype: 'json',
                success: function (response) {
                    $('head').append('<link href="//{$edm_url}/frontEndPackage/css/RWD_eidtor_style.css" rel="stylesheet" type="text/css">');
                    $('html').append('<script src="//{$edm_url}/frontEndPackage/js/template.js"><\/script>');
                    setTimeout(function(){
                        $('#rwd_edm_home').html(response);
                    }, 150);
                }
            });
        }
    </script>
@endsection
